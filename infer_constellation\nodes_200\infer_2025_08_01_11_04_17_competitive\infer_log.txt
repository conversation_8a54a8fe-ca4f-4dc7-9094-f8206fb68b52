推理数据数量: 100
每个序列任务数量: 200
星座卫星数量: 3
星座模式: competitive
模型: transformer+indrnn
种子: 12348
内存总量: 0.3
电量总量: 5
检查点路径: constellation_smp/constellation_smp100/constellation_transformerindrnn2025_07_31_21_30_00

批次 1:
  奖励值: 70.7739
  收益率: 1.0000
  距离: 35.6863
  内存使用: 0.6562
  能量使用: 0.9096
  推理时间: 17.0338秒

批次 2:
  奖励值: 75.5868
  收益率: 1.0000
  距离: 32.5733
  内存使用: 0.7245
  能量使用: 0.9285
  推理时间: 16.9983秒

批次 3:
  奖励值: 69.1884
  收益率: 1.0000
  距离: 37.5430
  内存使用: 0.6808
  能量使用: 0.9992
  推理时间: 20.9126秒

批次 4:
  奖励值: 77.2871
  收益率: 1.0000
  距离: 32.8667
  内存使用: 0.7542
  能量使用: 1.0049
  推理时间: 19.2947秒

批次 5:
  奖励值: 72.1338
  收益率: 1.0000
  距离: 32.5969
  内存使用: 0.7352
  能量使用: 1.0003
  推理时间: 18.4277秒

批次 6:
  奖励值: 78.4077
  收益率: 1.0000
  距离: 34.5115
  内存使用: 0.6450
  能量使用: 1.0281
  推理时间: 17.1926秒

批次 7:
  奖励值: 82.5985
  收益率: 1.0000
  距离: 30.9271
  内存使用: 0.6452
  能量使用: 1.0307
  推理时间: 16.6145秒

批次 8:
  奖励值: 75.8106
  收益率: 1.0000
  距离: 34.1837
  内存使用: 0.6562
  能量使用: 0.9389
  推理时间: 15.8141秒

批次 9:
  奖励值: 74.5436
  收益率: 1.0000
  距离: 33.4992
  内存使用: 0.6720
  能量使用: 0.9416
  推理时间: 15.5993秒

批次 10:
  奖励值: 80.0160
  收益率: 1.0000
  距离: 32.2940
  内存使用: 0.7632
  能量使用: 0.9107
  推理时间: 17.6086秒

