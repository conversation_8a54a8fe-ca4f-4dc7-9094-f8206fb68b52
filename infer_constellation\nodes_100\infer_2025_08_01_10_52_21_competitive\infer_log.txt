推理数据数量: 100
每个序列任务数量: 100
星座卫星数量: 3
星座模式: competitive
模型: transformer+indrnn
种子: 12348
内存总量: 0.3
电量总量: 5
检查点路径: constellation_smp/constellation_smp100/constellation_transformerindrnn2025_07_31_21_30_00

批次 1:
  奖励值: 36.7600
  收益率: 1.0000
  距离: 15.9910
  内存使用: 0.2144
  能量使用: 0.4747
  推理时间: 4.7619秒

批次 2:
  奖励值: 37.6877
  收益率: 1.0000
  距离: 16.7252
  内存使用: 0.2515
  能量使用: 0.4479
  推理时间: 5.3056秒

批次 3:
  奖励值: 37.6203
  收益率: 1.0000
  距离: 15.7975
  内存使用: 0.2209
  能量使用: 0.5410
  推理时间: 5.2770秒

批次 4:
  奖励值: 38.1982
  收益率: 1.0000
  距离: 15.3092
  内存使用: 0.1742
  能量使用: 0.5203
  推理时间: 5.2633秒

批次 5:
  奖励值: 35.9484
  收益率: 1.0000
  距离: 15.4061
  内存使用: 0.1881
  能量使用: 0.5089
  推理时间: 5.2708秒

批次 6:
  奖励值: 40.9675
  收益率: 1.0000
  距离: 15.6052
  内存使用: 0.1779
  能量使用: 0.4819
  推理时间: 5.3387秒

批次 7:
  奖励值: 36.4376
  收益率: 1.0000
  距离: 16.1896
  内存使用: 0.1953
  能量使用: 0.5401
  推理时间: 5.2373秒

批次 8:
  奖励值: 37.7414
  收益率: 1.0000
  距离: 16.8486
  内存使用: 0.1611
  能量使用: 0.5075
  推理时间: 5.2576秒

批次 9:
  奖励值: 37.5446
  收益率: 1.0000
  距离: 17.1799
  内存使用: 0.2066
  能量使用: 0.5242
  推理时间: 5.2322秒

批次 10:
  奖励值: 40.0397
  收益率: 1.0000
  距离: 14.0844
  内存使用: 0.5034
  能量使用: 0.5328
  推理时间: 5.2864秒

批次 11:
  奖励值: 36.7428
  收益率: 1.0000
  距离: 16.4167
  内存使用: 0.1785
  能量使用: 0.5191
  推理时间: 5.2199秒

批次 12:
  奖励值: 40.9047
  收益率: 1.0000
  距离: 13.1381
  内存使用: 0.1765
  能量使用: 0.4760
  推理时间: 5.2464秒

批次 13:
  奖励值: 39.2231
  收益率: 1.0000
  距离: 17.6628
  内存使用: 0.1951
  能量使用: 0.4960
  推理时间: 5.2175秒

批次 14:
  奖励值: 36.8647
  收益率: 1.0000
  距离: 15.7470
  内存使用: 0.1918
  能量使用: 0.5319
  推理时间: 5.2433秒

批次 15:
  奖励值: 39.4136
  收益率: 1.0000
  距离: 15.8234
  内存使用: 0.2141
  能量使用: 0.5352
  推理时间: 5.4602秒

批次 16:
  奖励值: 39.9748
  收益率: 1.0000
  距离: 15.8286
  内存使用: 0.1714
  能量使用: 0.4504
  推理时间: 5.2485秒

批次 17:
  奖励值: 39.2801
  收益率: 1.0000
  距离: 13.8608
  内存使用: 0.2176
  能量使用: 0.5614
  推理时间: 5.2024秒

批次 18:
  奖励值: 36.2912
  收益率: 1.0000
  距离: 16.2489
  内存使用: 0.2149
  能量使用: 0.5538
  推理时间: 5.2264秒

批次 19:
  奖励值: 39.7681
  收益率: 1.0000
  距离: 16.3656
  内存使用: 0.1763
  能量使用: 0.5218
  推理时间: 5.2300秒

批次 20:
  奖励值: 36.4506
  收益率: 1.0000
  距离: 16.0531
  内存使用: 0.1858
  能量使用: 0.4598
  推理时间: 5.2203秒

批次 21:
  奖励值: 39.4143
  收益率: 1.0000
  距离: 14.4993
  内存使用: 0.1585
  能量使用: 0.5135
  推理时间: 5.3065秒

批次 22:
  奖励值: 39.5299
  收益率: 1.0000
  距离: 14.3726
  内存使用: 0.2748
  能量使用: 0.4928
  推理时间: 5.2724秒

批次 23:
  奖励值: 39.1416
  收益率: 1.0000
  距离: 14.5840
  内存使用: 0.2454
  能量使用: 0.4874
  推理时间: 5.6648秒

批次 24:
  奖励值: 34.8279
  收益率: 1.0000
  距离: 16.9299
  内存使用: 0.2364
  能量使用: 0.5780
  推理时间: 5.1458秒

批次 25:
  奖励值: 40.2604
  收益率: 1.0000
  距离: 15.7944
  内存使用: 0.1651
  能量使用: 0.5197
  推理时间: 4.8797秒

批次 26:
  奖励值: 37.4507
  收益率: 1.0000
  距离: 16.8002
  内存使用: 0.1820
  能量使用: 0.5165
  推理时间: 4.7852秒

批次 27:
  奖励值: 39.9062
  收益率: 1.0000
  距离: 17.0904
  内存使用: 0.2108
  能量使用: 0.4864
  推理时间: 4.8095秒

批次 28:
  奖励值: 38.5582
  收益率: 1.0000
  距离: 14.8095
  内存使用: 0.2147
  能量使用: 0.4593
  推理时间: 4.9449秒

批次 29:
  奖励值: 36.4515
  收益率: 1.0000
  距离: 16.2399
  内存使用: 0.1721
  能量使用: 0.4851
  推理时间: 4.7677秒

批次 30:
  奖励值: 37.4243
  收益率: 1.0000
  距离: 16.8197
  内存使用: 0.1960
  能量使用: 0.5358
  推理时间: 5.4915秒

批次 31:
  奖励值: 41.7206
  收益率: 1.0000
  距离: 16.3145
  内存使用: 0.1735
  能量使用: 0.4708
  推理时间: 5.1210秒

批次 32:
  奖励值: 37.7231
  收益率: 1.0000
  距离: 17.6062
  内存使用: 0.2342
  能量使用: 0.5133
  推理时间: 4.7748秒

批次 33:
  奖励值: 37.7149
  收益率: 1.0000
  距离: 17.4992
  内存使用: 0.1688
  能量使用: 0.5021
  推理时间: 4.6793秒

批次 34:
  奖励值: 37.3423
  收益率: 1.0000
  距离: 17.6654
  内存使用: 0.1703
  能量使用: 0.4797
  推理时间: 4.7304秒

批次 35:
  奖励值: 38.9475
  收益率: 1.0000
  距离: 15.8098
  内存使用: 0.1906
  能量使用: 0.5046
  推理时间: 4.7065秒

批次 36:
  奖励值: 41.0282
  收益率: 1.0000
  距离: 17.4568
  内存使用: 0.1599
  能量使用: 0.5270
  推理时间: 4.7517秒

批次 37:
  奖励值: 32.9827
  收益率: 1.0000
  距离: 16.1466
  内存使用: 0.1867
  能量使用: 0.5012
  推理时间: 5.2631秒

批次 38:
  奖励值: 34.9426
  收益率: 1.0000
  距离: 16.2600
  内存使用: 0.1397
  能量使用: 0.5152
  推理时间: 5.2909秒

批次 39:
  奖励值: 37.9114
  收益率: 1.0000
  距离: 17.1228
  内存使用: 0.1417
  能量使用: 0.5126
  推理时间: 5.2586秒

批次 40:
  奖励值: 36.7899
  收益率: 1.0000
  距离: 15.7234
  内存使用: 0.1783
  能量使用: 0.5186
  推理时间: 5.3053秒

批次 41:
  奖励值: 35.8977
  收益率: 1.0000
  距离: 15.9296
  内存使用: 0.1952
  能量使用: 0.4798
  推理时间: 5.2863秒

批次 42:
  奖励值: 40.0439
  收益率: 1.0000
  距离: 15.8595
  内存使用: 0.1656
  能量使用: 0.4870
  推理时间: 5.2636秒

批次 43:
  奖励值: 37.6391
  收益率: 1.0000
  距离: 16.4367
  内存使用: 0.2054
  能量使用: 0.4796
  推理时间: 5.2557秒

批次 44:
  奖励值: 39.6172
  收益率: 1.0000
  距离: 15.7234
  内存使用: 0.1489
  能量使用: 0.4932
  推理时间: 5.3097秒

批次 45:
  奖励值: 37.8504
  收益率: 1.0000
  距离: 16.0364
  内存使用: 0.2165
  能量使用: 0.4463
  推理时间: 5.2938秒

批次 46:
  奖励值: 37.6727
  收益率: 1.0000
  距离: 16.4143
  内存使用: 0.1578
  能量使用: 0.4825
  推理时间: 5.3012秒

批次 47:
  奖励值: 35.6574
  收益率: 1.0000
  距离: 18.1905
  内存使用: 0.1871
  能量使用: 0.5077
  推理时间: 5.2708秒

批次 48:
  奖励值: 38.0670
  收益率: 1.0000
  距离: 16.8967
  内存使用: 0.4799
  能量使用: 0.4893
  推理时间: 5.3598秒

批次 49:
  奖励值: 40.0612
  收益率: 1.0000
  距离: 17.6727
  内存使用: 0.1566
  能量使用: 0.4484
  推理时间: 5.2624秒

批次 50:
  奖励值: 38.9944
  收益率: 1.0000
  距离: 17.1158
  内存使用: 0.2040
  能量使用: 0.4913
  推理时间: 5.2556秒

批次 51:
  奖励值: 39.0662
  收益率: 1.0000
  距离: 13.1707
  内存使用: 0.1887
  能量使用: 0.5082
  推理时间: 5.3169秒

批次 52:
  奖励值: 36.8737
  收益率: 1.0000
  距离: 17.9476
  内存使用: 0.2262
  能量使用: 0.4789
  推理时间: 5.2436秒

批次 53:
  奖励值: 39.4270
  收益率: 1.0000
  距离: 16.2296
  内存使用: 0.2371
  能量使用: 0.4792
  推理时间: 5.2721秒

批次 54:
  奖励值: 37.0396
  收益率: 1.0000
  距离: 16.8649
  内存使用: 0.1811
  能量使用: 0.4747
  推理时间: 5.2780秒

批次 55:
  奖励值: 39.4811
  收益率: 1.0000
  距离: 15.1547
  内存使用: 0.2105
  能量使用: 0.4727
  推理时间: 5.3113秒

批次 56:
  奖励值: 37.2719
  收益率: 1.0000
  距离: 14.9869
  内存使用: 0.1973
  能量使用: 0.4720
  推理时间: 5.3301秒

批次 57:
  奖励值: 35.8957
  收益率: 1.0000
  距离: 17.6461
  内存使用: 0.2161
  能量使用: 0.5465
  推理时间: 5.2438秒

批次 58:
  奖励值: 36.9054
  收益率: 1.0000
  距离: 15.2646
  内存使用: 0.1842
  能量使用: 0.5405
  推理时间: 5.2709秒

批次 59:
  奖励值: 41.0315
  收益率: 1.0000
  距离: 18.4728
  内存使用: 0.1647
  能量使用: 0.4994
  推理时间: 5.2838秒

批次 60:
  奖励值: 38.0550
  收益率: 1.0000
  距离: 15.7719
  内存使用: 0.2283
  能量使用: 0.4898
  推理时间: 5.2747秒

批次 61:
  奖励值: 37.6892
  收益率: 1.0000
  距离: 15.4795
  内存使用: 0.2137
  能量使用: 0.5284
  推理时间: 5.2485秒

批次 62:
  奖励值: 35.4469
  收益率: 1.0000
  距离: 14.2213
  内存使用: 0.1660
  能量使用: 0.5190
  推理时间: 5.2579秒

批次 63:
  奖励值: 34.1284
  收益率: 1.0000
  距离: 19.4059
  内存使用: 0.2374
  能量使用: 0.4998
  推理时间: 5.2582秒

批次 64:
  奖励值: 34.1755
  收益率: 1.0000
  距离: 16.3381
  内存使用: 0.2046
  能量使用: 0.5062
  推理时间: 5.2263秒

批次 65:
  奖励值: 41.0780
  收益率: 1.0000
  距离: 15.0647
  内存使用: 0.1671
  能量使用: 0.4122
  推理时间: 5.2881秒

批次 66:
  奖励值: 38.2473
  收益率: 1.0000
  距离: 17.6134
  内存使用: 0.1992
  能量使用: 0.4928
  推理时间: 5.3327秒

批次 67:
  奖励值: 38.2511
  收益率: 1.0000
  距离: 15.7793
  内存使用: 0.2048
  能量使用: 0.5137
  推理时间: 5.2657秒

批次 68:
  奖励值: 35.4160
  收益率: 1.0000
  距离: 18.0758
  内存使用: 0.2074
  能量使用: 0.4848
  推理时间: 5.2824秒

批次 69:
  奖励值: 37.0791
  收益率: 1.0000
  距离: 16.3190
  内存使用: 0.2077
  能量使用: 0.5152
  推理时间: 5.2700秒

批次 70:
  奖励值: 39.5621
  收益率: 1.0000
  距离: 17.3862
  内存使用: 0.1978
  能量使用: 0.4918
  推理时间: 5.2532秒

批次 71:
  奖励值: 37.9569
  收益率: 1.0000
  距离: 16.5830
  内存使用: 0.1812
  能量使用: 0.5218
  推理时间: 5.2578秒

批次 72:
  奖励值: 36.8026
  收益率: 1.0000
  距离: 16.5283
  内存使用: 0.1616
  能量使用: 0.5049
  推理时间: 5.2751秒

批次 73:
  奖励值: 42.8138
  收益率: 1.0000
  距离: 14.2164
  内存使用: 0.2166
  能量使用: 0.5395
  推理时间: 5.3465秒

批次 74:
  奖励值: 40.2818
  收益率: 1.0000
  距离: 16.3526
  内存使用: 0.2029
  能量使用: 0.4808
  推理时间: 5.2536秒

批次 75:
  奖励值: 37.2629
  收益率: 1.0000
  距离: 16.2212
  内存使用: 0.1966
  能量使用: 0.4564
  推理时间: 5.2986秒

批次 76:
  奖励值: 34.9689
  收益率: 1.0000
  距离: 15.9666
  内存使用: 0.1957
  能量使用: 0.4999
  推理时间: 5.3069秒

批次 77:
  奖励值: 40.0916
  收益率: 1.0000
  距离: 16.9410
  内存使用: 0.1909
  能量使用: 0.4849
  推理时间: 5.2940秒

批次 78:
  奖励值: 36.5708
  收益率: 1.0000
  距离: 16.9962
  内存使用: 0.1773
  能量使用: 0.4848
  推理时间: 5.3008秒

批次 79:
  奖励值: 34.1890
  收益率: 1.0000
  距离: 16.3653
  内存使用: 0.1754
  能量使用: 0.4814
  推理时间: 5.3651秒

批次 80:
  奖励值: 37.4957
  收益率: 1.0000
  距离: 14.3150
  内存使用: 0.1826
  能量使用: 0.5109
  推理时间: 5.1956秒

批次 81:
  奖励值: 31.1045
  收益率: 1.0000
  距离: 17.3084
  内存使用: 0.1994
  能量使用: 0.4832
  推理时间: 5.0509秒

批次 82:
  奖励值: 41.6447
  收益率: 1.0000
  距离: 14.7349
  内存使用: 0.2265
  能量使用: 0.5491
  推理时间: 4.8720秒

批次 83:
  奖励值: 40.0979
  收益率: 1.0000
  距离: 18.0856
  内存使用: 0.1884
  能量使用: 0.5302
  推理时间: 4.9100秒

批次 84:
  奖励值: 38.8851
  收益率: 1.0000
  距离: 17.1630
  内存使用: 0.2086
  能量使用: 0.4582
  推理时间: 4.8745秒

批次 85:
  奖励值: 39.5993
  收益率: 1.0000
  距离: 15.7058
  内存使用: 0.1833
  能量使用: 0.5123
  推理时间: 4.7479秒

批次 86:
  奖励值: 37.3114
  收益率: 1.0000
  距离: 15.0874
  内存使用: 0.1807
  能量使用: 0.5090
  推理时间: 5.3374秒

批次 87:
  奖励值: 36.8741
  收益率: 1.0000
  距离: 15.7386
  内存使用: 0.1750
  能量使用: 0.5381
  推理时间: 5.4079秒

批次 88:
  奖励值: 40.1771
  收益率: 1.0000
  距离: 14.6407
  内存使用: 0.1704
  能量使用: 0.4347
  推理时间: 5.4009秒

批次 89:
  奖励值: 36.4722
  收益率: 1.0000
  距离: 15.9760
  内存使用: 0.2231
  能量使用: 0.4565
  推理时间: 5.5209秒

批次 90:
  奖励值: 33.7611
  收益率: 1.0000
  距离: 18.9669
  内存使用: 0.1832
  能量使用: 0.5278
  推理时间: 5.4340秒

批次 91:
  奖励值: 37.9665
  收益率: 1.0000
  距离: 15.7768
  内存使用: 0.1792
  能量使用: 0.5111
  推理时间: 5.4042秒

批次 92:
  奖励值: 39.1926
  收益率: 1.0000
  距离: 18.3196
  内存使用: 0.2090
  能量使用: 0.4862
  推理时间: 5.4586秒

批次 93:
  奖励值: 42.0490
  收益率: 1.0000
  距离: 15.3581
  内存使用: 0.2330
  能量使用: 0.5108
  推理时间: 5.5260秒

批次 94:
  奖励值: 36.2433
  收益率: 1.0000
  距离: 16.0010
  内存使用: 0.2053
  能量使用: 0.5071
  推理时间: 5.5348秒

批次 95:
  奖励值: 39.2795
  收益率: 1.0000
  距离: 18.6057
  内存使用: 0.2040
  能量使用: 0.4314
  推理时间: 4.9269秒

批次 96:
  奖励值: 37.3832
  收益率: 1.0000
  距离: 17.3517
  内存使用: 0.2153
  能量使用: 0.4667
  推理时间: 5.0138秒

批次 97:
  奖励值: 37.2892
  收益率: 1.0000
  距离: 16.0973
  内存使用: 0.2286
  能量使用: 0.4958
  推理时间: 5.0015秒

批次 98:
  奖励值: 42.2731
  收益率: 1.0000
  距离: 14.6985
  内存使用: 0.2315
  能量使用: 0.5238
  推理时间: 5.0039秒

批次 99:
  奖励值: 33.7544
  收益率: 1.0000
  距离: 16.7857
  内存使用: 0.1921
  能量使用: 0.5135
  推理时间: 4.9895秒

批次 100:
  奖励值: 39.9692
  收益率: 1.0000
  距离: 17.8426
  内存使用: 0.1157
  能量使用: 0.5033
  推理时间: 5.0089秒


==================== 总结 ====================
平均收益率: 1.0000
平均能量使用: 0.4995
平均推理时间: 5.2038秒
