import argparse

parser = argparse.ArgumentParser(description='Combinatorial Optimization')
# model
# pn
# gpn
# transformer
parser.add_argument('--model', default='transformer')
# train
parser.add_argument('--task', default='constellation_smp')
parser.add_argument('--seed', default=12346, type=int)  # 12345
parser.add_argument('--checkpoint', default=None)
parser.add_argument('--test', action='store_true', default=False)
parser.add_argument('--max_grad_norm', default=1.0, type=float)  # 更严格的梯度裁剪
parser.add_argument('--dropout', default=0.2, type=float)  # 增加dropout以防止过拟合
# 学习率 - 针对Transformer优化
parser.add_argument('--actor_lr', default=1e-4, type=float)  # 降低学习率
parser.add_argument('--critic_lr', default=1e-4, type=float)  # 降低学习率
parser.add_argument('--weight_decay', type=float, default=1e-4, help='Weight decay (L2 regularization)')  # 增加正则化
parser.add_argument('--train_size', default=500, type=int)  # 减少训练集大小以加快训练
parser.add_argument('--valid_size', default=50, type=int)   # 减少验证集大小
parser.add_argument('--epochs', default=5, type=int)        # 增加训练轮数
parser.add_argument('--lr', type=float, default=1e-4, help="learning rate")  # 降低基础学习率
parser.add_argument('--nodes', dest='num_nodes', default=100, type=int)
parser.add_argument('--hidden', dest='hidden_size', default=128, type=int)  # 减少隐藏层大小
parser.add_argument('--batch_size', default=4, type=int)  # 进一步减少批次大小
parser.add_argument('--static_size', default=9, type=int)
parser.add_argument('--dynamic_size', default=7, type=int)

parser.add_argument('--memory_total', default=0.02, type=float)  # 进一步减少到0.02
parser.add_argument('--power_total', default=0.4, type=float)   # 进一步减少到0.4

# 星座相关参数
parser.add_argument('--num_satellites', default=3, type=int, help='卫星星座中的卫星数量')
parser.add_argument('--constellation_mode', default='competitive', type=str, 
                    help='星座工作模式: cooperative(协同), competitive(竞争), hybrid(混合)')
parser.add_argument('--task_sharing', action='store_true', default=True, 
                    help='是否允许卫星间共享任务信息')
parser.add_argument('--communication_delay', default=0.01, type=float, 
                    help='星座内卫星间通信延迟')
parser.add_argument('--satellite_distance', default=0.5, type=float,
                    help='卫星间平均距离（归一化）')
parser.add_argument('--verbose', action='store_true', default=True,
                    help='是否打印详细训练信息')

# MultiHead_Additive_Attention
parser.add_argument('--attention', default='MultiHead_Additive_Attention', type=str)
parser.add_argument('--n_head', default=8, type=int)

# Transformer specific parameters - 优化内存使用
parser.add_argument('--d_model', default=128, type=int, help='Transformer模型维度')  # 减少到128
parser.add_argument('--n_encoder_layers', default=3, type=int, help='Transformer编码器层数')  # 减少到3层
parser.add_argument('--n_decoder_layers', default=3, type=int, help='Transformer解码器层数')  # 减少到3层
parser.add_argument('--d_ff', default=256, type=int, help='前馈网络维度')  # 减少到256
parser.add_argument('--transformer_dropout', default=0.2, type=float, help='Transformer dropout率')  # 增加dropout
parser.add_argument('--max_seq_len', default=200, type=int, help='最大序列长度')  # 减少到200
parser.add_argument('--use_spatial_temporal_encoding', action='store_true', default=True,
                    help='是否使用时空位置编码')
parser.add_argument('--transformer_lr_factor', default=0.5, type=float,
                    help='Transformer学习率调整因子')  # 降低学习率因子
parser.add_argument('--use_gradient_checkpointing', action='store_true', default=True,
                    help='是否使用梯度检查点以节省内存')

# lstm
# indrnn
# indrnnv2
parser.add_argument('--rnn', default='indrnn', type=str)
parser.add_argument('--layers', dest='num_layers', default=2, type=int)

# conv1d
parser.add_argument('--encoder', default='conv1d', type=str)

args = parser.parse_args()
