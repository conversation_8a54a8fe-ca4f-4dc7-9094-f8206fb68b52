import sys
import os
import csv

# 添加项目根目录到系统路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

import torch
from torch.utils.data import DataLoader
from constellation_smp.gpn_constellation import GPNConstellation
from constellation_smp.transformer_constellation import GPNTransformerConstellation
from constellation_smp.constellation_smp import ConstellationSMPDataset, reward, render
import time
import datetime
import numpy as np
import os
from hyperparameter import args as hyperparameter_args

# 推理参数
CHECKPOINT_PATH = 'constellation_smp/constellation_smp100/constellation_transformerindrnn2025_07_31_21_30_00'
NUM_SATELLITES = 3
MEMORY_TOTAL = 0.3
POWER_TOTAL = 5
INFER_NUM_DATA = 100
SEED = 12348
BATCH_SIZE = 1
RENDER_RESULTS = True
OUTPUT_DIR = 'infer_constellation'

# 定义要测试的任务节点数量列表
NODE_SCALES = [1000]  # 使用训练时支持的最大节点数

def get_model_config_from_checkpoint(checkpoint_path):
    """
    从检查点目录的log.txt文件中读取并解析模型配置。
    """
    log_file_path = os.path.join(checkpoint_path, 'log.txt')
    default_config = {
        'constellation_mode': 'competitive',
        'model': 'gpn',
        'rnn': 'indrnn'
    }

    if not os.path.exists(log_file_path):
        print(f"警告: 在 {checkpoint_path} 中未找到 log.txt。使用默认配置。")
        return default_config

    config = default_config.copy()

    try:
        # 尝试多种编码方式
        encodings = ['utf-8', 'gbk', 'gb2312', 'latin-1']
        content = None

        for encoding in encodings:
            try:
                with open(log_file_path, 'r', encoding=encoding) as f:
                    content = f.read()
                print(f"成功使用 {encoding} 编码读取日志文件")
                break
            except UnicodeDecodeError:
                continue

        if content is None:
            print("无法使用任何编码读取日志文件，使用默认配置。")
            return default_config

        for line in content.split('\n'):
            line = line.strip()
            if ':' in line:
                key, value = line.split(':', 1)
                key = key.strip().lower()
                value = value.strip().lower()

                if key == 'constellation_mode' or '星座模式' in key:
                    if value in ['cooperative', 'competitive', 'hybrid']:
                        config['constellation_mode'] = value
                elif key == 'model':
                    config['model'] = value
                elif key == 'rnn':
                    config['rnn'] = value

        print(f"从检查点检测到配置: {config}")
        return config

    except Exception as e:
        print(f"读取或解析 log.txt 时出错: {e}。使用默认配置。")
        return default_config

def run_inference_for_nodes(num_nodes, device, model_config):
    """
    为指定数量的节点运行推理
    """
    constellation_mode = model_config['constellation_mode']
    model_type = model_config['model']
    rnn_type = model_config['rnn']

    print(f"\n{'='*20} 开始为 {num_nodes} 个节点在 {constellation_mode} 模式下运行推理 {'='*20}")
    print(f"模型类型: {model_type} + {rnn_type}")

    # 创建输出目录
    scale_output_dir = os.path.join(OUTPUT_DIR, f'nodes_{num_nodes}')
    if not os.path.exists(scale_output_dir):
        os.makedirs(scale_output_dir)

    # 设置静态和动态大小
    static_size = getattr(hyperparameter_args, 'static_size', 9)
    dynamic_size = getattr(hyperparameter_args, 'dynamic_size', 7)

    # 创建测试数据集
    try:
        test_data = ConstellationSMPDataset(
            num_nodes,
            INFER_NUM_DATA,
            SEED,
            MEMORY_TOTAL,
            POWER_TOTAL,
            NUM_SATELLITES
        )
        test_loader = DataLoader(test_data, BATCH_SIZE, False, num_workers=0)
    except Exception as e:
        print(f"创建数据集时出错 (节点数: {num_nodes}): {e}")
        return

    # 根据模型类型创建模型
    try:
        print(f"检测到的模型类型: '{model_type}'")
        print(f"模型类型比较结果: {model_type == 'transformer'}")
        if model_type == 'transformer':
            # 使用Transformer模型 - 确保参数与训练时一致
            # 确保max_seq_len足够大以容纳当前的节点数
            required_seq_len = num_nodes + 1  # +1 for start node
            model_max_seq_len = max(getattr(hyperparameter_args, 'max_seq_len', 1000), required_seq_len)

            print(f"创建Transformer模型，参数:")
            print(f"  d_model: {getattr(hyperparameter_args, 'd_model', 256)}")
            print(f"  n_heads: {getattr(hyperparameter_args, 'n_head', 8)}")
            print(f"  n_encoder_layers: {getattr(hyperparameter_args, 'n_encoder_layers', 6)}")
            print(f"  n_decoder_layers: {getattr(hyperparameter_args, 'n_decoder_layers', 6)}")
            print(f"  d_ff: {getattr(hyperparameter_args, 'd_ff', 1024)}")
            print(f"  dropout: {getattr(hyperparameter_args, 'transformer_dropout', 0.1)}")
            print(f"  max_seq_len: {model_max_seq_len} (required: {required_seq_len})")

            actor = GPNTransformerConstellation(
                static_size=static_size,
                dynamic_size=dynamic_size,
                d_model=getattr(hyperparameter_args, 'd_model', 256),
                n_heads=getattr(hyperparameter_args, 'n_head', 8),
                n_encoder_layers=getattr(hyperparameter_args, 'n_encoder_layers', 6),
                n_decoder_layers=getattr(hyperparameter_args, 'n_decoder_layers', 6),
                d_ff=getattr(hyperparameter_args, 'd_ff', 1024),
                num_satellites=NUM_SATELLITES,
                constellation_mode=constellation_mode,
                dropout=getattr(hyperparameter_args, 'transformer_dropout', 0.1),
                max_seq_len=model_max_seq_len,
                update_fn=test_data.update_dynamic,
                mask_fn=test_data.update_mask,
                num_nodes=num_nodes
            ).to(device)
        else:
            # 使用原始GPN模型
            actor = GPNConstellation(
                static_size,
                dynamic_size,
                hyperparameter_args.hidden_size,
                NUM_SATELLITES,
                rnn_type,
                hyperparameter_args.num_layers,
                test_data.update_dynamic,
                test_data.update_mask,
                num_nodes,
                hyperparameter_args.dropout,
                constellation_mode=constellation_mode
            ).to(device)
    except Exception as e:
        print(f"创建模型时出错 (节点数: {num_nodes}): {e}")
        return
    
    # 加载模型权重
    try:
        actor_path = os.path.join(CHECKPOINT_PATH, 'actor.pt')
        checkpoint = torch.load(actor_path, map_location=device)

        # 处理位置编码尺寸不匹配的问题
        if model_type == 'transformer':
            current_state = actor.state_dict()

            # 检查位置编码尺寸
            pe_key = 'decoder.positional_encoding.pe'
            if pe_key in checkpoint and pe_key in current_state:
                checkpoint_pe_shape = checkpoint[pe_key].shape
                current_pe_shape = current_state[pe_key].shape

                if checkpoint_pe_shape != current_pe_shape:
                    print(f"位置编码尺寸不匹配: 检查点 {checkpoint_pe_shape} vs 当前模型 {current_pe_shape}")
                    print("扩展位置编码以适应更大的序列长度...")

                    # 如果当前模型需要更大的位置编码，扩展检查点中的位置编码
                    if current_pe_shape[0] > checkpoint_pe_shape[0]:
                        # 创建新的位置编码
                        import math
                        max_len = current_pe_shape[0]
                        d_model = current_pe_shape[2]

                        pe = torch.zeros(max_len, d_model)
                        position = torch.arange(0, max_len, dtype=torch.float).unsqueeze(1)
                        div_term = torch.exp(torch.arange(0, d_model, 2).float() *
                                           (-math.log(10000.0) / d_model))
                        pe[:, 0::2] = torch.sin(position * div_term)
                        pe[:, 1::2] = torch.cos(position * div_term)
                        pe = pe.unsqueeze(0).transpose(0, 1)

                        checkpoint[pe_key] = pe
                        print(f"位置编码已扩展到 {pe.shape}")

        actor.load_state_dict(checkpoint)
        actor.eval()
        print("模型权重加载成功")
    except Exception as e:
        print(f"加载模型权重时出错 (节点数: {num_nodes}): {e}")
        return
    
    # 创建保存目录
    now = '%s' % datetime.datetime.now().strftime('%Y_%m_%d_%H_%M_%S')
    save_dir = os.path.join(scale_output_dir, f'infer_{now}_{constellation_mode}')
    if not os.path.exists(save_dir):
        os.makedirs(save_dir)
    
    # 创建详细结果的CSV文件
    detailed_csv_path = os.path.join(save_dir, 'inference_details.csv')
    
    # 创建日志文件
    infer_log_path = os.path.join(save_dir, 'infer_log.txt')
    with open(infer_log_path, 'a+', encoding='utf-8') as f:
        f.write(f'推理数据数量: {INFER_NUM_DATA}\n')
        f.write(f'每个序列任务数量: {num_nodes}\n')
        f.write(f'星座卫星数量: {NUM_SATELLITES}\n')
        f.write(f'星座模式: {constellation_mode}\n')
        f.write(f'模型: {hyperparameter_args.model}+{hyperparameter_args.rnn}\n')
        f.write(f'种子: {SEED}\n')
        f.write(f'内存总量: {MEMORY_TOTAL}\n')
        f.write(f'电量总量: {POWER_TOTAL}\n')
        f.write(f'检查点路径: {CHECKPOINT_PATH}\n\n')
    
    # 评估指标
    plan_revenue_rates, infer_times, powers = [], [], []
    
    # 使用 'with' 语句确保CSV文件被正确关闭
    with open(detailed_csv_path, 'w', newline='', encoding='utf-8') as csvfile:
        csv_writer = csv.writer(csvfile)
        # 写入CSV表头
        csv_writer.writerow(['batch_idx', 'reward', 'revenue_rate', 'distance', 'memory_usage', 'power_usage', 'inference_time_s'])

        # 遍历测试数据
        for batch_idx, batch in enumerate(test_loader):
            if batch_idx >= INFER_NUM_DATA:
                break
                
            static, dynamic, _ = batch
            static = static.to(device)
            dynamic = dynamic.to(device)

            # 打印任务总量信息
            if batch_idx == 0:
                print(f'总收益: {torch.sum(static[:, 4, :], dim=1).item()}')
                print(f'总内存需求: {torch.sum(static[:, 5, :], dim=1).item()}')
                print(f'总电量需求: {torch.sum(static[:, 6, :], dim=1).item()}')
                print(f'每颗卫星内存供应: {MEMORY_TOTAL}')
                print(f'每颗卫星电量供应: {POWER_TOTAL}')
                
            # 推理时间开始
            start_time = time.time()
            
            # 模型推理
            try:
                with torch.no_grad():
                    tour_indices, satellite_indices, _ = actor.forward(static, dynamic)
                
                # 计算奖励和其他指标，传入星座模式
                reward_value, revenue_rate, distance, memory, power = reward(static, tour_indices, satellite_indices, constellation_mode)
            except Exception as e:
                print(f"批次 {batch_idx} 推理过程中出错 (节点数: {num_nodes}): {e}")
                continue
            
            # 推理时间结束
            end_time = time.time()
            infer_time = end_time - start_time
            
            # 提取标量值以便记录
            reward_val = reward_value.mean().item()
            revenue_rate_val = revenue_rate.mean().item()
            distance_val = distance.mean().item()
            memory_val = memory.mean().item()
            power_val = power.mean().item()
            
            # 保存指标
            plan_revenue_rates.append(revenue_rate_val)
            powers.append(power_val)
            infer_times.append(infer_time)
            
            # 打印每个批次的结果
            print(f'批次 {batch_idx}/{INFER_NUM_DATA}:')
            print(f'  奖励值: {reward_val:.4f}')
            print(f'  收益率: {revenue_rate_val:.4f}')
            print(f'  距离: {distance_val:.4f}')
            print(f'  内存使用: {memory_val:.4f}')
            print(f'  能量使用: {power_val:.4f}')
            print(f'  推理时间: {infer_time:.4f}秒')
            
            # 记录到日志
            with open(infer_log_path, 'a+', encoding='utf-8') as f:
                f.write(f'批次 {batch_idx + 1}:\n')
                f.write(f'  奖励值: {reward_val:.4f}\n')
                f.write(f'  收益率: {revenue_rate_val:.4f}\n')
                f.write(f'  距离: {distance_val:.4f}\n')
                f.write(f'  内存使用: {memory_val:.4f}\n')
                f.write(f'  能量使用: {power_val:.4f}\n')
                f.write(f'  推理时间: {infer_time:.4f}秒\n\n')

            # 将详细结果写入CSV
            csv_writer.writerow([batch_idx, f'{reward_val:.4f}', f'{revenue_rate_val:.4f}', f'{distance_val:.4f}', f'{memory_val:.4f}', f'{power_val:.4f}', f'{infer_time:.4f}'])

            # 渲染结果
            if RENDER_RESULTS:
                img_save_path = os.path.join(save_dir, f'batch_{batch_idx}_nodes_{num_nodes}.png')
                # 始终渲染批次中的第一个样本（索引为0），并传入卫星数量
                render(static, tour_indices, satellite_indices, img_save_path, NUM_SATELLITES, 0)
            
    # 计算平均指标
    avg_revenue_rate = np.mean(plan_revenue_rates) if plan_revenue_rates else 0
    avg_power = np.mean(powers) if powers else 0
    avg_infer_time = np.mean(infer_times) if infer_times else 0
    
    # 打印和记录总结
    summary = (
        f'推理完成 (节点数: {num_nodes}):\n'
        f'  平均收益率: {avg_revenue_rate:.4f}\n'
        f'  平均能量使用: {avg_power:.4f}\n'
        f'  平均推理时间: {avg_infer_time:.4f}秒'
    )
    print(summary)
    with open(infer_log_path, 'a+', encoding='utf-8') as f:
        f.write('\n' + '='*20 + ' 总结 ' + '='*20 + '\n')
        f.write(f'平均收益率: {avg_revenue_rate:.4f}\n')
        f.write(f'平均能量使用: {avg_power:.4f}\n')
        f.write(f'平均推理时间: {avg_infer_time:.4f}秒\n')

    # 确保在出错时也能返回
    if not plan_revenue_rates:
        return 0, 0, 0
        
    return avg_revenue_rate, avg_power, avg_infer_time

def main():
    # 设置设备
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f"使用设备: {device}")
    
    # 确保检查点路径存在
    if not os.path.exists(CHECKPOINT_PATH) or not os.path.exists(os.path.join(CHECKPOINT_PATH, 'actor.pt')):
        print(f"错误: 检查点路径或actor.pt不存在于: {CHECKPOINT_PATH}")
        return
    
    # 确保根输出目录存在
    if not os.path.exists(OUTPUT_DIR):
        os.makedirs(OUTPUT_DIR)
        
    # 从检查点自动检测模型配置
    model_config = get_model_config_from_checkpoint(CHECKPOINT_PATH)
    constellation_mode = model_config['constellation_mode']

    # 创建总结果的CSV文件
    summary_csv_path = os.path.join(OUTPUT_DIR, 'inference_summary.csv')
    with open(summary_csv_path, 'w', newline='', encoding='utf-8') as summary_file:
        summary_writer = csv.writer(summary_file)
        summary_writer.writerow(['num_nodes', 'constellation_mode', 'model_type', 'avg_revenue_rate', 'avg_power_usage', 'avg_inference_time_s'])

        # 为每个节点规模运行推理
        for num_nodes in NODE_SCALES:
            result = run_inference_for_nodes(num_nodes, device, model_config)
            if result is None:
                print(f"节点数 {num_nodes} 的推理运行失败，跳过...")
                continue
            avg_revenue_rate, avg_power, avg_infer_time = result
            # 将总结结果写入CSV
            summary_writer.writerow([num_nodes, constellation_mode, f"{model_config['model']}+{model_config['rnn']}", f'{avg_revenue_rate:.4f}', f'{avg_power:.4f}', f'{avg_infer_time:.4f}'])

if __name__ == '__main__':
    main() 