"""
分析星座任务规划中的问题诊断脚本
"""
import torch
import numpy as np
from constellation_smp.constellation_smp import ConstellationSMPDataset, reward
from constellation_smp.transformer_constellation import GPNTransformerConstellation
from hyperparameter import args
import os

def analyze_resource_constraints():
    """分析资源约束是否合理"""
    print("=== 资源约束分析 ===")
    
    # 参数设置
    memory_total = args.memory_total  # 0.3
    power_total = args.power_total    # 5.0
    memory_consume_max = 0.01  # 单任务最大内存消耗
    power_consume_max = 0.01   # 单任务最大能量消耗
    num_nodes = 1000
    
    print(f"内存总量: {memory_total}")
    print(f"能量总量: {power_total}")
    print(f"单任务最大内存消耗: {memory_consume_max}")
    print(f"单任务最大能量消耗: {power_consume_max}")
    print(f"任务数量: {num_nodes}")
    
    # 计算理论上可执行的最大任务数
    max_tasks_by_memory = memory_total / memory_consume_max
    max_tasks_by_power = power_total / power_consume_max
    
    print(f"\n理论上可执行的最大任务数:")
    print(f"  按内存限制: {max_tasks_by_memory:.0f} 个任务")
    print(f"  按能量限制: {max_tasks_by_power:.0f} 个任务")
    print(f"  实际任务数: {num_nodes} 个任务")
    
    if max_tasks_by_memory >= num_nodes and max_tasks_by_power >= num_nodes:
        print("⚠️  警告: 资源约束过于宽松，单颗卫星理论上可以完成所有任务！")
        return True
    else:
        print("✅ 资源约束合理")
        return False

def analyze_satellite_workload_balance():
    """分析卫星负载均衡机制"""
    print("\n=== 卫星负载均衡分析 ===")
    
    # 创建测试数据
    test_data = ConstellationSMPDataset(
        size=100,  # 较小的测试规模
        num_samples=1,
        seed=42,
        memory_total=args.memory_total,
        power_total=args.power_total,
        num_satellites=args.num_satellites
    )
    
    static, dynamic, _ = test_data[0]
    static = static.unsqueeze(0)  # 添加batch维度
    dynamic = dynamic.unsqueeze(0)
    
    print(f"静态特征形状: {static.shape}")
    print(f"动态特征形状: {dynamic.shape}")
    
    # 分析每颗卫星的初始资源
    print(f"\n每颗卫星的初始资源:")
    for sat_idx in range(args.num_satellites):
        memory_surplus = dynamic[0, 2, :, sat_idx]
        power_surplus = dynamic[0, 3, :, sat_idx]
        print(f"  卫星 {sat_idx}: 内存={memory_surplus[0]:.3f}, 能量={power_surplus[0]:.3f}")
    
    # 分析任务的资源需求分布
    memory_requirements = static[0, 5, 1:]  # 排除起始节点
    power_requirements = static[0, 6, 1:]
    
    print(f"\n任务资源需求统计:")
    print(f"  内存需求: 平均={memory_requirements.mean():.4f}, 最大={memory_requirements.max():.4f}")
    print(f"  能量需求: 平均={power_requirements.mean():.4f}, 最大={power_requirements.max():.4f}")
    
    return static, dynamic

def analyze_model_decision_pattern(checkpoint_path):
    """分析模型的决策模式"""
    print(f"\n=== 模型决策模式分析 ===")
    
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    
    # 创建模型
    actor = GPNTransformerConstellation(
        static_size=args.static_size,
        dynamic_size=args.dynamic_size,
        d_model=256,
        n_heads=8,
        n_encoder_layers=6,
        n_decoder_layers=6,
        d_ff=1024,
        num_satellites=args.num_satellites,
        constellation_mode=args.constellation_mode,
        dropout=0.1,
        max_seq_len=1000,
        num_nodes=100
    ).to(device)
    
    # 加载权重
    try:
        actor_path = os.path.join(checkpoint_path, 'actor.pt')
        checkpoint = torch.load(actor_path, map_location=device)
        actor.load_state_dict(checkpoint)
        actor.eval()
        print("✅ 模型加载成功")
    except Exception as e:
        print(f"❌ 模型加载失败: {e}")
        return
    
    # 创建测试数据
    test_data = ConstellationSMPDataset(
        size=100,
        num_samples=5,
        seed=42,
        memory_total=args.memory_total,
        power_total=args.power_total,
        num_satellites=args.num_satellites
    )
    
    satellite_task_counts = []
    
    for i in range(5):
        static, dynamic, _ = test_data[i]
        static = static.unsqueeze(0).to(device)
        dynamic = dynamic.unsqueeze(0).to(device)
        
        with torch.no_grad():
            tour_indices, satellite_indices, _ = actor.forward(static, dynamic)
        
        # 统计每颗卫星执行的任务数
        sat_counts = torch.zeros(args.num_satellites)
        for sat_idx in satellite_indices[0]:
            if sat_idx.item() < args.num_satellites:
                sat_counts[sat_idx.item()] += 1
        
        satellite_task_counts.append(sat_counts.numpy())
        
        print(f"样本 {i+1}: 卫星任务分配 = {sat_counts.numpy()}")
    
    # 计算平均分配
    avg_counts = np.mean(satellite_task_counts, axis=0)
    std_counts = np.std(satellite_task_counts, axis=0)
    
    print(f"\n平均任务分配:")
    for sat_idx in range(args.num_satellites):
        print(f"  卫星 {sat_idx}: {avg_counts[sat_idx]:.1f} ± {std_counts[sat_idx]:.1f} 个任务")
    
    # 计算负载不均衡程度
    max_load = np.max(avg_counts)
    min_load = np.min(avg_counts)
    imbalance_ratio = max_load / (min_load + 1e-8)
    
    print(f"\n负载不均衡比率: {imbalance_ratio:.2f}")
    if imbalance_ratio > 3.0:
        print("⚠️  警告: 存在严重的负载不均衡问题！")
    elif imbalance_ratio > 2.0:
        print("⚠️  警告: 存在中等程度的负载不均衡")
    else:
        print("✅ 负载分配相对均衡")

def main():
    print("星座任务规划问题诊断")
    print("=" * 50)
    
    # 1. 分析资源约束
    resource_issue = analyze_resource_constraints()
    
    # 2. 分析卫星负载均衡
    static, dynamic = analyze_satellite_workload_balance()
    
    # 3. 分析模型决策模式
    checkpoint_path = "constellation_smp/constellation_smp100/constellation_transformerindrnn2025_07_31_21_30_00"
    if os.path.exists(checkpoint_path):
        analyze_model_decision_pattern(checkpoint_path)
    else:
        print(f"❌ 检查点路径不存在: {checkpoint_path}")
    
    print("\n" + "=" * 50)
    print("诊断总结:")
    if resource_issue:
        print("🔴 主要问题: 资源约束过于宽松，导致单颗卫星可以完成大部分任务")
        print("💡 建议: 减少memory_total和power_total，或增加任务的资源消耗")
    
    print("\n建议的修复措施:")
    print("1. 调整资源参数: memory_total=0.05, power_total=1.0")
    print("2. 增加任务间的距离惩罚权重")
    print("3. 在模型中加强负载均衡机制")
    print("4. 考虑添加卫星切换成本")

if __name__ == "__main__":
    main()
