"""
星座任务规划的训练脚本
"""
import os
import time
import datetime
import numpy as np
import torch
import torch.nn as nn
import torch.nn.functional as F
import torch.optim as optim
from torch.utils.data import DataLoader
from constellation_smp.constellation_smp import ConstellationSMPDataset, reward, render
from constellation_smp.gpn_constellation import GPNConstellation, ConstellationStateCritic
from constellation_smp.transformer_constellation import GPNTransformerConstellation, TransformerStateCritic
from hyperparameter import args
from pict import plot_single_smp_train_loss

device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')

# 全局日志文件对象
log_file = None

def log_message(message, save_to_file=True, to_console=True):
    """
    将消息同时输出到控制台和日志文件
    """
    if to_console:
        print(message)
    if save_to_file and log_file is not None:
        log_file.write(message + '\n')
        log_file.flush()


def validate_constellation_smp(data_loader, actor, reward_fn, num_satellites, render_fn=None, save_dir='.', num_plot=5, verbose=False, constellation_mode='cooperative'):
    """
    验证星座任务规划模型
    """
    actor.eval()
    if not os.path.exists(save_dir):
        os.makedirs(save_dir)

    total_reward = 0
    total_revenue_rate = 0
    total_distance = 0
    total_memory = 0
    total_power = 0
    total_samples = 0
    
    for batch_idx, batch in enumerate(data_loader):
        static, dynamic, x0 = batch
        static = static.to(device)
        dynamic = dynamic.to(device)
        batch_size = static.size(0)

        with torch.no_grad():
            tour_indices, satellite_indices, _ = actor.forward(static, dynamic)
        
        # 获取详细指标
        reward, revenue_rate, distance, memory, power = reward_fn(static, tour_indices, satellite_indices, constellation_mode)
        
        total_samples += batch_size
        total_reward += reward.sum().item()
        total_revenue_rate += revenue_rate.sum().item()
        total_distance += distance.sum().item()
        total_memory += memory.sum().item()
        total_power += power.sum().item()

        # 计算平均值
        reward_mean = reward.mean().item()
        revenue_rate_mean = revenue_rate.mean().item()
        distance_mean = distance.mean().item()
        memory_mean = memory.mean().item()
        power_mean = power.mean().item()
        
        # 打印每个batch的结果
        log_message('Test Batch %d/%d, reward: %2.3f, revenue_rate: %2.4f, distance: %2.4f, memory: %2.4f, power: %2.4f' %
              (batch_idx, len(data_loader), reward_mean, revenue_rate_mean, distance_mean, memory_mean, power_mean),
              to_console=verbose)
        
        if render_fn is not None and batch_idx < num_plot:
            name = 'test_batch%d_%2.4f.png' % (batch_idx, revenue_rate_mean)
            path = os.path.join(save_dir, name)
            render_fn(static, tour_indices, satellite_indices, path, num_satellites, 0)
    
    # 计算平均指标
    avg_reward = total_reward / total_samples if total_samples > 0 else 0
    avg_revenue_rate = total_revenue_rate / total_samples if total_samples > 0 else 0
    avg_distance = total_distance / total_samples if total_samples > 0 else 0
    avg_memory = total_memory / total_samples if total_samples > 0 else 0
    avg_power = total_power / total_samples if total_samples > 0 else 0
    
    log_message('Test Summary - Avg reward: %2.3f, revenue_rate: %2.4f, distance: %2.4f, memory: %2.4f, power: %2.4f' %
          (avg_reward, avg_revenue_rate, avg_distance, avg_memory, avg_power))
    
    actor.train()
    return avg_reward, avg_revenue_rate, avg_distance, avg_memory, avg_power


def train_constellation_smp():
    """
    训练星座任务规划模型的主函数
    """
    global log_file
    # 创建数据集
    train_data = ConstellationSMPDataset(
        args.num_nodes, 
        args.train_size, 
        args.seed, 
        args.memory_total, 
        args.power_total,
        args.num_satellites
    )
    
    valid_data = ConstellationSMPDataset(
        args.num_nodes, 
        args.valid_size, 
        args.seed + 1, 
        args.memory_total, 
        args.power_total,
        args.num_satellites
    )
    
    # 创建保存目录
    now = '%s' % datetime.datetime.now().strftime('%Y_%m_%d_%H_%M_%S')
    save_dir = os.path.join(args.task, args.task + '%d' % args.num_nodes, 'constellation_' + str(args.model) + str(args.rnn) + now)
    if not os.path.exists(save_dir):
        os.makedirs(save_dir)

    # 创建并打开日志文件
    f_dir = os.path.join(save_dir, 'log.txt')
    log_file = open(f_dir, 'a+')

    # 保存训练信息
    log_message(args.task + ': ' + str(args.num_nodes))
    log_message('model: ' + str(args.model))
    log_message('rnn: ' + str(args.rnn))
    log_message('hidden_size: ' + str(args.hidden_size))
    log_message('batch_size: ' + str(args.batch_size))
    log_message('seed: ' + str(args.seed))
    log_message('train-size: ' + str(args.train_size))
    log_message('valid-size: ' + str(args.valid_size))
    log_message('epochs: ' + str(args.epochs))
    log_message('lr: ' + str(args.lr))
    log_message('memory_total: ' + str(args.memory_total))
    log_message('power_total: ' + str(args.power_total))
    log_message('dropout: ' + str(args.dropout))
    log_message('actor_lr: ' + str(args.actor_lr))
    log_message('critic_lr: ' + str(args.critic_lr))
    log_message('num_satellites: ' + str(args.num_satellites))
    log_message('constellation_mode: ' + str(args.constellation_mode))
    log_message('verbose: ' + str(args.verbose))
    log_message(now)

    # 创建模型
    if args.model == 'transformer':
        # 使用Transformer模型
        actor = GPNTransformerConstellation(
            static_size=args.static_size,
            dynamic_size=args.dynamic_size,
            d_model=getattr(args, 'd_model', 256),
            n_heads=args.n_head,
            n_encoder_layers=getattr(args, 'n_encoder_layers', 6),
            n_decoder_layers=getattr(args, 'n_decoder_layers', 6),
            d_ff=getattr(args, 'd_ff', 1024),
            num_satellites=args.num_satellites,
            constellation_mode=args.constellation_mode,
            dropout=getattr(args, 'transformer_dropout', 0.1),
            max_seq_len=getattr(args, 'max_seq_len', 1000),
            update_fn=train_data.update_dynamic,
            mask_fn=train_data.update_mask,
            num_nodes=args.num_nodes,
            use_checkpoint=getattr(args, 'use_gradient_checkpointing', False)
        ).to(device)

        critic = TransformerStateCritic(
            static_size=args.static_size,
            dynamic_size=args.dynamic_size,
            d_model=getattr(args, 'd_model', 256),
            n_heads=args.n_head,
            n_layers=getattr(args, 'n_encoder_layers', 4),
            d_ff=getattr(args, 'd_ff', 1024),
            num_satellites=args.num_satellites,
            constellation_mode=args.constellation_mode,
            dropout=getattr(args, 'transformer_dropout', 0.1),
            max_seq_len=getattr(args, 'max_seq_len', 1000)
        ).to(device)
    else:
        # 使用原始GPN模型
        actor = GPNConstellation(
            args.static_size,
            args.dynamic_size,
            args.hidden_size,
            args.num_satellites,
            args.rnn,
            args.num_layers,
            train_data.update_dynamic,
            train_data.update_mask,
            args.num_nodes,
            args.dropout,
            args.constellation_mode
        ).to(device)

        critic = ConstellationStateCritic(
            args.static_size,
            args.dynamic_size,
            args.hidden_size,
            args.num_satellites,
            args.constellation_mode
        ).to(device)

    # 转换参数为字典
    kwargs = vars(args)
    kwargs['train_data'] = train_data
    kwargs['valid_data'] = valid_data
    kwargs['reward_fn'] = reward
    kwargs['render_fn'] = render
    kwargs['save_dir'] = save_dir

    # 加载检查点（如果有）
    if args.checkpoint:
        path = os.path.join(args.checkpoint, 'actor.pt')
        actor.load_state_dict(torch.load(path, device))
        path = os.path.join(args.checkpoint, 'critic.pt')
        critic.load_state_dict(torch.load(path, device))

    # 训练或测试
    if not args.test:
        train_constellation_smp_process(actor, critic, **kwargs)

    # 测试模型
    test_data = ConstellationSMPDataset(
        args.num_nodes, 
        args.valid_size, 
        args.seed + 2, 
        args.memory_total, 
        args.power_total,
        args.num_satellites
    )
    
    test_dir = os.path.join(save_dir, 'test_constellation')
    test_loader = DataLoader(test_data, args.batch_size, False, num_workers=0)
    log_message("\n开始测试模型...")
    out, revenue_rate_avg, distance_avg, memory_avg, power_avg = validate_constellation_smp(test_loader, actor, reward, args.num_satellites, render, test_dir, num_plot=5, verbose=args.verbose, constellation_mode=args.constellation_mode)
    log_message('测试完成 - 平均星座收益率: %2.4f' % revenue_rate_avg)

    if log_file is not None:
        log_file.close()


def train_constellation_smp_process(actor, critic, task, num_nodes, train_data, valid_data, reward_fn,
                                   render_fn, batch_size, actor_lr, critic_lr, max_grad_norm,
                                   attention, epochs, num_satellites, save_dir, weight_decay, verbose=False, constellation_mode='cooperative', **kwargs):
    """
    星座任务规划模型的训练过程
    """
    # 根据模型类型调整学习率
    model_type = kwargs.get('model', 'gpn')
    if model_type == 'transformer':
        # Transformer模型通常需要更小的学习率
        transformer_lr_factor = kwargs.get('transformer_lr_factor', 0.5)
        actor_lr = actor_lr * transformer_lr_factor
        critic_lr = critic_lr * transformer_lr_factor

        # 添加学习率预热
        warmup_steps = 100
        log_message(f"使用Transformer模型，学习率调整因子: {transformer_lr_factor}")
        log_message(f"调整后学习率 - Actor: {actor_lr:.6f}, Critic: {critic_lr:.6f}")

    # 定义优化器，并加入权重衰减
    actor_optim = optim.Adam(actor.parameters(), lr=actor_lr, weight_decay=weight_decay)
    critic_optim = optim.Adam(critic.parameters(), lr=critic_lr, weight_decay=weight_decay)
    
    # 结合使用 ReduceLROnPlateau 和 StepLR
    actor_scheduler_plateau = optim.lr_scheduler.ReduceLROnPlateau(
        actor_optim, 
        mode='max', 
        factor=0.5, 
        patience=5,  # 增加patience
        verbose=True
    )
    critic_scheduler_plateau = optim.lr_scheduler.ReduceLROnPlateau(
        critic_optim, 
        mode='max', 
        factor=0.5, 
        patience=5,  # 增加patience
        verbose=True
    )

    # 定义数据加载器 - 优化性能
    num_workers = min(4, os.cpu_count() // 2)  # 限制worker数量，避免过多开销
    train_loader = DataLoader(train_data, batch_size, True, num_workers=num_workers, pin_memory=True)
    valid_loader = DataLoader(valid_data, batch_size, False, num_workers=num_workers, pin_memory=True)
    best_params = None
    best_reward = -np.inf  # 初始化为负无穷，因为我们要最大化奖励
    times, losses, rewards, critic_rewards, revenue_rates, distances, memories, powers = [], [], [], [], [], [], [], []

    # 开始训练
    for epoch in range(epochs):
        # 创建每个epoch的保存目录
        epoch_dir = os.path.join(save_dir, 'epoch%s' % epoch)
        if not os.path.exists(epoch_dir):
            os.makedirs(epoch_dir)

        # 开始训练
        actor.train()
        critic.train()
        epoch_start = time.time()
        start = epoch_start
        
        log_message(f"\n开始训练 Epoch {epoch+1}/{epochs}")
        
        for batch_idx, batch in enumerate(train_loader):
            static, dynamic, x0 = batch
            static = static.to(device)
            dynamic = dynamic.to(device)
            
            # 前向传播
            tour_indices, satellite_indices, tour_log_prob = actor(static, dynamic)
            
            # 计算奖励
            reward, revenue_rate, distance, memory, power = reward_fn(static, tour_indices, satellite_indices, constellation_mode)

            # 评估基线
            critic_est = critic(static, dynamic).view(-1)
            advantage = (reward - critic_est)

            # 计算critic损失，添加数值稳定性检查
            critic_loss = torch.mean(advantage ** 2)

            # 检查数值稳定性
            if torch.isnan(critic_loss) or torch.isinf(critic_loss):
                log_message(f"警告：检测到异常的critic损失值: {critic_loss.item()}")
                continue

            # 优势归一化，添加更强的数值稳定性
            advantage_std = advantage.std()
            if advantage_std < 1e-6:
                advantage_std = 1.0  # 避免除以接近零的数
            advantage = (advantage - advantage.mean()) / advantage_std

            # 计算actor损失
            actor_loss = torch.mean(-advantage.detach() * tour_log_prob.sum(dim=1))

            # 检查actor损失的数值稳定性
            if torch.isnan(actor_loss) or torch.isinf(actor_loss):
                log_message(f"警告：检测到异常的actor损失值: {actor_loss.item()}")
                continue
            
            # 优化器步骤
            actor_optim.zero_grad()
            actor_loss.backward()
            torch.nn.utils.clip_grad_norm_(actor.parameters(), max_grad_norm)
            actor_optim.step()
            
            critic_optim.zero_grad()
            critic_loss.backward()
            torch.nn.utils.clip_grad_norm_(critic.parameters(), max_grad_norm)
            critic_optim.step()
            
            # 记录指标
            losses.append(critic_loss.item())
            rewards.append(reward.mean().item())
            critic_rewards.append(critic_est.mean().item())
            revenue_rates.append(revenue_rate.mean().item())
            distances.append(distance.mean().item())
            memories.append(memory.mean().item())
            powers.append(power.mean().item())
            
            # 打印训练进度
            if (batch_idx + 1) % 10 == 0:
                end = time.time()
                times.append(end - start)
                start = end
                
                log_message('Epoch %d, Batch %d/%d, loss: %2.3f, reward: %2.3f, critic_reward: %2.3f, revenue_rate: %2.4f, distance: %2.4f, memory: %2.4f, power: %2.4f, lr: %.6f, took: %.3fs' %
                      (epoch + 1, batch_idx + 1, len(train_loader),
                       np.mean(losses[-10:]), np.mean(rewards[-10:]), np.mean(critic_rewards[-10:]), 
                       np.mean(revenue_rates[-10:]), np.mean(distances[-10:]), np.mean(memories[-10:]), np.mean(powers[-10:]),
                       actor_optim.param_groups[0]['lr'],
                       times[-1]),
                      to_console=True)
        
        # 每个epoch结束后进行验证
        log_message("开始验证...")
        valid_reward, valid_revenue_rate, valid_distance, valid_memory, valid_power = validate_constellation_smp(valid_loader, actor, reward_fn, num_satellites, render_fn, epoch_dir, num_plot=5, verbose=True, constellation_mode=constellation_mode)
        log_message('验证完成 - Epoch %d, reward: %2.3f, revenue_rate: %2.4f, distance: %2.4f, memory: %2.4f, power: %2.4f' %
              (epoch + 1, valid_reward, valid_revenue_rate, valid_distance, valid_memory, valid_power))
        
        # 更新学习率
        actor_scheduler_plateau.step(valid_reward)
        critic_scheduler_plateau.step(valid_reward)
        
        # 保存最佳模型
        if valid_reward > best_reward:
            best_reward = valid_reward
            actor_save_path = os.path.join(save_dir, 'actor.pt')
            critic_save_path = os.path.join(save_dir, 'critic.pt')
            torch.save(actor.state_dict(), actor_save_path)
            torch.save(critic.state_dict(), critic_save_path)
            log_message(f"已保存新模型到 {save_dir} (验证集奖励: {best_reward:.4f})")
        
        # 绘制训练曲线
        plot_single_smp_train_loss(
            save_dir, 
            np.array(times).cumsum(), 
            losses, rewards, critic_rewards, 
            revenue_rates, distances, memories, powers
        )
        
    log_message("训练完成")


if __name__ == '__main__':
    train_constellation_smp() 