# Transformer集成使用指南

## 概述
本项目已成功集成Transformer架构到敏捷观察卫星星座任务规划系统中，以提升模型在大规模任务中的性能表现。

## 主要特性
- 🚀 基于Transformer的序列建模，更好地捕获长距离依赖
- 🌍 专门的时空位置编码，适应卫星任务的时空特性
- 🤝 支持多种星座工作模式（协同、竞争、混合）
- ⚡ 并行计算提高训练效率
- 📊 完整的性能对比和评估工具

## 快速开始

### 1. 使用Transformer模型训练
```bash
# 使用Transformer模型训练星座任务规划
python train_constellation.py --model transformer --epochs 10 --batch_size 32

# 指定Transformer特定参数
python train_constellation.py \
    --model transformer \
    --d_model 256 \
    --n_encoder_layers 6 \
    --n_decoder_layers 6 \
    --n_head 8 \
    --d_ff 1024 \
    --transformer_dropout 0.1
```

### 2. 模型对比测试
```bash
# 运行基本功能测试
python test_transformer.py

# 运行完整的训练对比
python train_comparison.py
```

### 3. 使用原始GPN模型（向后兼容）
```bash
# 继续使用原始GPN模型
python train_constellation.py --model gpn
```

## 模型架构

### Transformer编码器
- **ConstellationTransformerEncoder**: 处理多卫星状态的编码器
- **SpatialTemporalEncoding**: 时空位置编码
- **MultiHeadAttention**: 多头自注意力机制
- **TransformerEncoderLayer**: 标准Transformer编码器层

### Transformer解码器
- **ConstellationTransformerDecoder**: 自回归任务选择解码器
- **TransformerDecoderLayer**: 带交叉注意力的解码器层
- 支持掩码注意力防止信息泄露

### 完整模型
- **GPNTransformerConstellation**: 主要的策略网络
- **TransformerStateCritic**: 基于Transformer的价值网络

## 超参数配置

### Transformer特定参数
```python
--d_model 256              # Transformer模型维度
--n_encoder_layers 6       # 编码器层数
--n_decoder_layers 6       # 解码器层数
--n_head 8                 # 注意力头数
--d_ff 1024               # 前馈网络维度
--transformer_dropout 0.1  # Dropout率
--max_seq_len 1000        # 最大序列长度
--transformer_lr_factor 0.5 # 学习率调整因子
```

### 星座模式
```python
--constellation_mode cooperative  # 协同模式（完全信息共享）
--constellation_mode competitive  # 竞争模式（无信息共享）
--constellation_mode hybrid      # 混合模式（门控信息共享）
```

## 性能指标

模型主要优化以下性能指标：
- **reward**: 综合奖励函数（收益 - 距离 - 资源消耗）
- **revenue_rate**: 收益率（已完成收益/总可能收益）
- **distance**: 总移动距离
- **memory**: 内存使用效率
- **power**: 能量消耗效率

## 文件结构

```
constellation_smp/
├── transformer_constellation.py  # Transformer模型实现
├── gpn_constellation.py         # 原始GPN模型
└── constellation_smp.py         # 数据集和奖励函数

train_constellation.py            # 主训练脚本
test_transformer.py              # 模型测试脚本
train_comparison.py              # 性能对比脚本
hyperparameter.py               # 超参数配置
```

## 模型对比结果

从初步测试可以看到：

### 模型复杂度
- **Transformer模型**: ~1.26M 参数
- **原始GPN模型**: ~0.70M 参数

### 序列处理能力
- **Transformer**: 支持完整序列长度，更好的全局建模
- **GPN**: 序列长度受RNN限制

### 训练效率
- **Transformer**: 并行计算，训练速度更快
- **GPN**: 序列计算，训练相对较慢

## 进一步优化建议

1. **超参数调优**
   - 调整学习率调度策略
   - 优化模型维度和层数
   - 实验不同的dropout率

2. **架构改进**
   - 添加残差连接和层归一化
   - 实验不同的注意力机制
   - 优化位置编码策略

3. **训练策略**
   - 使用预训练和微调
   - 实现课程学习
   - 添加正则化技术

## 故障排除

### 常见问题
1. **内存不足**: 减少batch_size或模型维度
2. **训练不稳定**: 降低学习率或增加梯度裁剪
3. **收敛慢**: 调整学习率调度或优化器参数

### 调试工具
- 使用`test_transformer.py`验证模型基本功能
- 检查梯度流和参数更新
- 监控训练过程中的损失变化

## 贡献指南

欢迎提交改进建议和bug报告！主要改进方向：
- 新的注意力机制设计
- 更高效的位置编码
- 针对卫星任务的特殊优化
- 性能基准测试

---

**注意**: 这是一个研究项目，建议在生产环境使用前进行充分的测试和验证。
