"""
测试Transformer模型修复效果的脚本
验证内存使用、损失收敛和revenue_rate是否正常
"""
import os
import torch
import torch.nn as nn
import numpy as np
from torch.utils.data import DataLoader
import psutil
import gc

from constellation_smp.constellation_smp import ConstellationSMPDataset, reward
from constellation_smp.transformer_constellation import GPNTransformerConstellation, TransformerStateCritic
from hyperparameter import args

device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')

def get_memory_usage():
    """获取当前内存使用情况"""
    process = psutil.Process(os.getpid())
    memory_info = process.memory_info()
    return memory_info.rss / 1024 / 1024  # MB

def test_model_creation():
    """测试模型创建和内存使用"""
    print("=== 测试模型创建 ===")
    
    initial_memory = get_memory_usage()
    print(f"初始内存使用: {initial_memory:.2f} MB")
    
    # 创建小规模测试数据
    test_data = ConstellationSMPDataset(
        num_nodes=20,  # 减少节点数进行测试
        size=10,       # 减少样本数
        seed=12345,
        memory_total=args.memory_total,
        power_total=args.power_total,
        num_satellites=args.num_satellites
    )
    
    data_memory = get_memory_usage()
    print(f"创建数据后内存使用: {data_memory:.2f} MB (+{data_memory-initial_memory:.2f} MB)")
    
    # 创建模型
    actor = GPNTransformerConstellation(
        static_size=args.static_size,
        dynamic_size=args.dynamic_size,
        d_model=args.d_model,
        n_heads=args.n_head,
        n_encoder_layers=args.n_encoder_layers,
        n_decoder_layers=args.n_decoder_layers,
        d_ff=args.d_ff,
        num_satellites=args.num_satellites,
        constellation_mode=args.constellation_mode,
        dropout=args.transformer_dropout,
        max_seq_len=args.max_seq_len,
        update_fn=test_data.update_dynamic,
        mask_fn=test_data.update_mask,
        num_nodes=20,
        use_checkpoint=args.use_gradient_checkpointing
    ).to(device)
    
    model_memory = get_memory_usage()
    print(f"创建模型后内存使用: {model_memory:.2f} MB (+{model_memory-data_memory:.2f} MB)")
    
    # 计算模型参数数量
    total_params = sum(p.numel() for p in actor.parameters())
    trainable_params = sum(p.numel() for p in actor.parameters() if p.requires_grad)
    
    print(f"模型总参数数: {total_params:,}")
    print(f"可训练参数数: {trainable_params:,}")
    print(f"模型大小估计: {total_params * 4 / 1024 / 1024:.2f} MB")
    
    return actor, test_data

def test_forward_pass(actor, test_data):
    """测试前向传播"""
    print("\n=== 测试前向传播 ===")
    
    test_loader = DataLoader(test_data, batch_size=2, shuffle=False, num_workers=0)
    
    actor.eval()
    with torch.no_grad():
        for batch_idx, batch in enumerate(test_loader):
            if batch_idx >= 1:  # 只测试一个batch
                break
                
            static, dynamic, _ = batch
            static = static.to(device)
            dynamic = dynamic.to(device)
            
            forward_memory_before = get_memory_usage()
            print(f"前向传播前内存: {forward_memory_before:.2f} MB")
            
            try:
                tour_indices, satellite_indices, log_probs = actor(static, dynamic)
                
                forward_memory_after = get_memory_usage()
                print(f"前向传播后内存: {forward_memory_after:.2f} MB (+{forward_memory_after-forward_memory_before:.2f} MB)")
                
                print(f"输出形状:")
                print(f"  tour_indices: {tour_indices.shape}")
                print(f"  satellite_indices: {satellite_indices.shape}")
                print(f"  log_probs: {log_probs.shape}")
                
                # 测试奖励计算
                reward_val, revenue_rate, distance, memory, power = reward(
                    static, tour_indices, satellite_indices, args.constellation_mode
                )
                
                print(f"奖励计算结果:")
                print(f"  reward: {reward_val.mean().item():.4f} (范围: {reward_val.min().item():.4f} ~ {reward_val.max().item():.4f})")
                print(f"  revenue_rate: {revenue_rate.mean().item():.4f} (范围: {revenue_rate.min().item():.4f} ~ {revenue_rate.max().item():.4f})")
                print(f"  distance: {distance.mean().item():.4f}")
                print(f"  memory: {memory.mean().item():.4f}")
                print(f"  power: {power.mean().item():.4f}")
                
                # 检查数值稳定性
                if torch.isnan(reward_val).any() or torch.isinf(reward_val).any():
                    print("❌ 警告：奖励值包含NaN或Inf")
                else:
                    print("✅ 奖励值数值稳定")
                
                if revenue_rate.min() < 0 or revenue_rate.max() > 1:
                    print("❌ 警告：revenue_rate超出[0,1]范围")
                else:
                    print("✅ revenue_rate在合理范围内")
                    
            except Exception as e:
                print(f"❌ 前向传播失败: {e}")
                return False
    
    return True

def test_training_step(actor, test_data):
    """测试训练步骤"""
    print("\n=== 测试训练步骤 ===")
    
    # 创建critic
    critic = TransformerStateCritic(
        static_size=args.static_size,
        dynamic_size=args.dynamic_size,
        d_model=args.d_model,
        n_heads=args.n_head,
        n_layers=args.n_encoder_layers,
        d_ff=args.d_ff,
        num_satellites=args.num_satellites,
        constellation_mode=args.constellation_mode,
        dropout=args.transformer_dropout,
        max_seq_len=args.max_seq_len
    ).to(device)
    
    # 创建优化器
    actor_optim = torch.optim.Adam(actor.parameters(), lr=args.actor_lr, weight_decay=args.weight_decay)
    critic_optim = torch.optim.Adam(critic.parameters(), lr=args.critic_lr, weight_decay=args.weight_decay)
    
    test_loader = DataLoader(test_data, batch_size=2, shuffle=False, num_workers=0)
    
    actor.train()
    critic.train()
    
    for batch_idx, batch in enumerate(test_loader):
        if batch_idx >= 1:  # 只测试一个batch
            break
            
        static, dynamic, _ = batch
        static = static.to(device)
        dynamic = dynamic.to(device)
        
        training_memory_before = get_memory_usage()
        print(f"训练前内存: {training_memory_before:.2f} MB")
        
        try:
            # 前向传播
            tour_indices, satellite_indices, tour_log_prob = actor(static, dynamic)
            
            # 计算奖励
            reward_val, revenue_rate, distance, memory, power = reward(
                static, tour_indices, satellite_indices, args.constellation_mode
            )
            
            # 评估基线
            critic_est = critic(static, dynamic).view(-1)
            advantage = (reward_val - critic_est)
            
            # 计算损失
            critic_loss = torch.mean(advantage ** 2)
            
            # 优势归一化
            advantage_std = advantage.std()
            if advantage_std < 1e-6:
                advantage_std = 1.0
            advantage = (advantage - advantage.mean()) / advantage_std
            
            actor_loss = torch.mean(-advantage.detach() * tour_log_prob.sum(dim=1))
            
            print(f"损失值:")
            print(f"  actor_loss: {actor_loss.item():.6f}")
            print(f"  critic_loss: {critic_loss.item():.6f}")
            print(f"  advantage mean: {advantage.mean().item():.6f}")
            print(f"  advantage std: {advantage.std().item():.6f}")
            
            # 检查损失的数值稳定性
            if torch.isnan(actor_loss) or torch.isinf(actor_loss):
                print("❌ Actor损失包含NaN或Inf")
                return False
            if torch.isnan(critic_loss) or torch.isinf(critic_loss):
                print("❌ Critic损失包含NaN或Inf")
                return False
            
            # 反向传播
            actor_optim.zero_grad()
            actor_loss.backward()
            torch.nn.utils.clip_grad_norm_(actor.parameters(), args.max_grad_norm)
            actor_optim.step()
            
            critic_optim.zero_grad()
            critic_loss.backward()
            torch.nn.utils.clip_grad_norm_(critic.parameters(), args.max_grad_norm)
            critic_optim.step()
            
            training_memory_after = get_memory_usage()
            print(f"训练后内存: {training_memory_after:.2f} MB (+{training_memory_after-training_memory_before:.2f} MB)")
            
            print("✅ 训练步骤成功完成")
            
        except Exception as e:
            print(f"❌ 训练步骤失败: {e}")
            return False
    
    return True

def main():
    """主测试函数"""
    print("开始测试Transformer模型修复效果...")
    print(f"使用设备: {device}")
    print(f"当前超参数:")
    print(f"  d_model: {args.d_model}")
    print(f"  n_encoder_layers: {args.n_encoder_layers}")
    print(f"  n_decoder_layers: {args.n_decoder_layers}")
    print(f"  d_ff: {args.d_ff}")
    print(f"  batch_size: {args.batch_size}")
    print(f"  max_seq_len: {args.max_seq_len}")
    print(f"  use_gradient_checkpointing: {args.use_gradient_checkpointing}")
    
    try:
        # 测试模型创建
        actor, test_data = test_model_creation()
        
        # 测试前向传播
        if not test_forward_pass(actor, test_data):
            print("❌ 前向传播测试失败")
            return
        
        # 测试训练步骤
        if not test_training_step(actor, test_data):
            print("❌ 训练步骤测试失败")
            return
        
        print("\n✅ 所有测试通过！模型修复效果良好。")
        
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {e}")
    finally:
        # 清理内存
        gc.collect()
        if torch.cuda.is_available():
            torch.cuda.empty_cache()

if __name__ == '__main__':
    main()
