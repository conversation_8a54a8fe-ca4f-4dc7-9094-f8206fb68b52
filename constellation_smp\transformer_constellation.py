"""
基于Transformer的星座任务规划模型
"""
import torch
import torch.nn as nn
import torch.nn.functional as F
import torch.utils.checkpoint as checkpoint
import math
import numpy as np
from typing import Optional, Tuple

device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')


class PositionalEncoding(nn.Module):
    """
    位置编码模块，为序列中的每个位置添加位置信息
    """
    def __init__(self, d_model: int, max_len: int = 5000, dropout: float = 0.1):
        super(PositionalEncoding, self).__init__()
        self.dropout = nn.Dropout(p=dropout)
        
        # 创建位置编码矩阵
        pe = torch.zeros(max_len, d_model)
        position = torch.arange(0, max_len, dtype=torch.float).unsqueeze(1)
        
        # 计算div_term用于sin和cos函数
        div_term = torch.exp(torch.arange(0, d_model, 2).float() * 
                           (-math.log(10000.0) / d_model))
        
        pe[:, 0::2] = torch.sin(position * div_term)
        pe[:, 1::2] = torch.cos(position * div_term)
        pe = pe.unsqueeze(0).transpose(0, 1)
        
        # 注册为buffer，不参与梯度更新
        self.register_buffer('pe', pe)
    
    def forward(self, x: torch.Tensor) -> torch.Tensor:
        """
        x: (seq_len, batch_size, d_model)
        """
        x = x + self.pe[:x.size(0), :]
        return self.dropout(x)


class SpatialTemporalEncoding(nn.Module):
    """
    时空位置编码，专门为卫星任务规划设计
    结合时间和空间位置信息
    """
    def __init__(self, d_model: int, dropout: float = 0.1):
        super(SpatialTemporalEncoding, self).__init__()
        self.d_model = d_model
        self.dropout = nn.Dropout(p=dropout)
        
        # 时间编码投影
        self.time_proj = nn.Linear(1, d_model // 2)
        # 空间编码投影
        self.spatial_proj = nn.Linear(1, d_model // 2)
        
        # 层归一化
        self.layer_norm = nn.LayerNorm(d_model)
    
    def forward(self, x: torch.Tensor, time_info: torch.Tensor, 
                spatial_info: torch.Tensor) -> torch.Tensor:
        """
        x: (batch_size, seq_len, d_model) - 输入特征
        time_info: (batch_size, seq_len, 1) - 时间信息
        spatial_info: (batch_size, seq_len, 1) - 空间位置信息
        """
        batch_size, seq_len, _ = x.shape
        
        # 编码时间信息
        time_encoding = self.time_proj(time_info)  # (batch_size, seq_len, d_model//2)
        
        # 编码空间信息
        spatial_encoding = self.spatial_proj(spatial_info)  # (batch_size, seq_len, d_model//2)
        
        # 拼接时空编码
        spatiotemporal_encoding = torch.cat([time_encoding, spatial_encoding], dim=-1)
        
        # 添加到输入特征
        x = x + spatiotemporal_encoding
        
        return self.layer_norm(self.dropout(x))


class MultiHeadAttention(nn.Module):
    """
    多头自注意力机制，针对星座任务规划优化
    """
    def __init__(self, d_model: int, n_heads: int, dropout: float = 0.1):
        super(MultiHeadAttention, self).__init__()
        assert d_model % n_heads == 0
        
        self.d_model = d_model
        self.n_heads = n_heads
        self.d_k = d_model // n_heads
        
        # 线性投影层
        self.w_q = nn.Linear(d_model, d_model)
        self.w_k = nn.Linear(d_model, d_model)
        self.w_v = nn.Linear(d_model, d_model)
        self.w_o = nn.Linear(d_model, d_model)
        
        # Dropout
        self.dropout = nn.Dropout(dropout)
        
        # 缩放因子
        self.scale = math.sqrt(self.d_k)
    
    def forward(self, query: torch.Tensor, key: torch.Tensor, value: torch.Tensor,
                mask: Optional[torch.Tensor] = None) -> Tuple[torch.Tensor, torch.Tensor]:
        """
        query: (batch_size, query_len, d_model)
        key, value: (batch_size, key_len, d_model)
        mask: (batch_size, query_len, key_len) or None
        """
        batch_size, query_len, d_model = query.shape
        key_len = key.shape[1]

        # 线性投影并重塑为多头形式
        Q = self.w_q(query).view(batch_size, query_len, self.n_heads, self.d_k).transpose(1, 2)
        K = self.w_k(key).view(batch_size, key_len, self.n_heads, self.d_k).transpose(1, 2)
        V = self.w_v(value).view(batch_size, key_len, self.n_heads, self.d_k).transpose(1, 2)
        
        # 计算注意力
        attention_output, attention_weights = self.scaled_dot_product_attention(
            Q, K, V, mask, self.scale
        )
        
        # 重塑并通过输出投影
        attention_output = attention_output.transpose(1, 2).contiguous().view(
            batch_size, query_len, d_model
        )
        output = self.w_o(attention_output)
        
        return output, attention_weights
    
    def scaled_dot_product_attention(self, Q: torch.Tensor, K: torch.Tensor, 
                                   V: torch.Tensor, mask: Optional[torch.Tensor],
                                   scale: float) -> Tuple[torch.Tensor, torch.Tensor]:
        """
        缩放点积注意力
        """
        # 计算注意力分数
        scores = torch.matmul(Q, K.transpose(-2, -1)) / scale
        
        # 应用掩码
        if mask is not None:
            mask = mask.unsqueeze(1).expand(-1, self.n_heads, -1, -1)
            scores = scores.masked_fill(mask == 0, -1e9)
        
        # Softmax归一化
        attention_weights = F.softmax(scores, dim=-1)
        attention_weights = self.dropout(attention_weights)
        
        # 应用注意力权重
        output = torch.matmul(attention_weights, V)
        
        return output, attention_weights


class FeedForward(nn.Module):
    """
    前馈神经网络
    """
    def __init__(self, d_model: int, d_ff: int, dropout: float = 0.1):
        super(FeedForward, self).__init__()
        self.linear1 = nn.Linear(d_model, d_ff)
        self.linear2 = nn.Linear(d_ff, d_model)
        self.dropout = nn.Dropout(dropout)
        self.activation = nn.GELU()
    
    def forward(self, x: torch.Tensor) -> torch.Tensor:
        return self.linear2(self.dropout(self.activation(self.linear1(x))))


class TransformerEncoderLayer(nn.Module):
    """
    Transformer编码器层 - 支持梯度检查点
    """
    def __init__(self, d_model: int, n_heads: int, d_ff: int, dropout: float = 0.1, use_checkpoint: bool = False):
        super(TransformerEncoderLayer, self).__init__()

        # 多头自注意力
        self.self_attention = MultiHeadAttention(d_model, n_heads, dropout)

        # 前馈网络
        self.feed_forward = FeedForward(d_model, d_ff, dropout)

        # 层归一化
        self.norm1 = nn.LayerNorm(d_model)
        self.norm2 = nn.LayerNorm(d_model)

        # Dropout
        self.dropout = nn.Dropout(dropout)

        # 梯度检查点
        self.use_checkpoint = use_checkpoint

    def forward(self, x: torch.Tensor, mask: Optional[torch.Tensor] = None) -> torch.Tensor:
        """
        x: (batch_size, seq_len, d_model)
        mask: (batch_size, seq_len, seq_len) or None
        """
        if self.use_checkpoint and self.training:
            return checkpoint.checkpoint(self._forward_impl, x, mask)
        else:
            return self._forward_impl(x, mask)

    def _forward_impl(self, x: torch.Tensor, mask: Optional[torch.Tensor] = None) -> torch.Tensor:
        # 自注意力 + 残差连接 + 层归一化
        attn_output, _ = self.self_attention(x, x, x, mask)
        x = self.norm1(x + self.dropout(attn_output))

        # 前馈网络 + 残差连接 + 层归一化
        ff_output = self.feed_forward(x)
        x = self.norm2(x + self.dropout(ff_output))

        return x


class ConstellationTransformerEncoder(nn.Module):
    """
    星座任务规划的Transformer编码器
    处理多颗卫星的状态并生成任务表示
    """
    def __init__(self, input_size: int, d_model: int, n_heads: int, n_layers: int,
                 d_ff: int, num_satellites: int, constellation_mode: str = 'cooperative',
                 dropout: float = 0.1, max_seq_len: int = 1000, use_checkpoint: bool = False):
        super(ConstellationTransformerEncoder, self).__init__()

        self.d_model = d_model
        self.num_satellites = num_satellites
        self.constellation_mode = constellation_mode
        self.use_checkpoint = use_checkpoint

        # 输入投影层
        self.input_projection = nn.Linear(input_size, d_model)

        # 时空位置编码
        self.spatiotemporal_encoding = SpatialTemporalEncoding(d_model, dropout)

        # Transformer编码器层 - 支持梯度检查点
        self.encoder_layers = nn.ModuleList([
            TransformerEncoderLayer(d_model, n_heads, d_ff, dropout, use_checkpoint)
            for _ in range(n_layers)
        ])

        # 卫星特定的编码器
        self.satellite_encoders = nn.ModuleList([
            nn.Sequential(
                nn.Linear(d_model, d_model),
                nn.LayerNorm(d_model),
                nn.GELU(),
                nn.Dropout(dropout)
            ) for _ in range(num_satellites)
        ])

        # 星座级别的特征融合
        if constellation_mode == 'cooperative':
            # 协同模式：完全信息共享
            self.constellation_fusion = nn.MultiheadAttention(
                embed_dim=d_model,
                num_heads=n_heads,
                dropout=dropout,
                batch_first=True
            )
        elif constellation_mode == 'hybrid':
            # 混合模式：门控信息共享
            self.constellation_fusion = nn.MultiheadAttention(
                embed_dim=d_model,
                num_heads=n_heads,
                dropout=dropout,
                batch_first=True
            )
            self.gate = nn.Sequential(
                nn.Linear(d_model * num_satellites, d_model),
                nn.Sigmoid()
            )
        # 竞争模式不需要额外的融合层

        # 输出投影
        self.output_projection = nn.Linear(d_model, d_model)
        self.output_norm = nn.LayerNorm(d_model)

        # 初始化参数
        self._init_parameters()

    def _init_parameters(self):
        """初始化模型参数"""
        for p in self.parameters():
            if p.dim() > 1:
                nn.init.xavier_uniform_(p)

    def forward(self, static: torch.Tensor, dynamic: torch.Tensor) -> Tuple[torch.Tensor, torch.Tensor]:
        """
        static: (batch_size, static_size, seq_len)
        dynamic: (batch_size, dynamic_size, seq_len, num_satellites)

        返回:
        constellation_features: (batch_size, seq_len, d_model) - 星座级别的特征
        satellite_features: (batch_size, num_satellites, seq_len, d_model) - 每颗卫星的特征
        """
        batch_size, static_size, seq_len = static.shape
        dynamic_size = dynamic.shape[1]

        # 为每颗卫星处理特征
        satellite_features = []

        for sat_idx in range(self.num_satellites):
            # 获取当前卫星的动态状态
            sat_dynamic = dynamic[:, :, :, sat_idx]  # (batch_size, dynamic_size, seq_len)

            # 拼接静态和动态特征
            sat_input = torch.cat([static, sat_dynamic], dim=1)  # (batch_size, input_size, seq_len)
            sat_input = sat_input.transpose(1, 2)  # (batch_size, seq_len, input_size)

            # 输入投影
            sat_features = self.input_projection(sat_input)  # (batch_size, seq_len, d_model)

            # 添加时空位置编码
            time_info = static[:, 0:1, :].transpose(1, 2)  # (batch_size, seq_len, 1)
            spatial_info = static[:, 1:2, :].transpose(1, 2)  # (batch_size, seq_len, 1)
            sat_features = self.spatiotemporal_encoding(sat_features, time_info, spatial_info)

            # 通过Transformer编码器层
            for encoder_layer in self.encoder_layers:
                sat_features = encoder_layer(sat_features)

            # 卫星特定的处理
            sat_features = self.satellite_encoders[sat_idx](sat_features)

            satellite_features.append(sat_features)

        # 堆叠所有卫星的特征
        satellite_features_stack = torch.stack(satellite_features, dim=1)  # (batch_size, num_satellites, seq_len, d_model)

        # 根据星座模式进行特征融合
        constellation_features = self._fuse_constellation_features(satellite_features_stack)

        # 输出投影和归一化
        constellation_features = self.output_norm(self.output_projection(constellation_features))

        return constellation_features, satellite_features_stack

    def _fuse_constellation_features(self, satellite_features: torch.Tensor) -> torch.Tensor:
        """
        融合星座特征
        satellite_features: (batch_size, num_satellites, seq_len, d_model)
        返回: (batch_size, seq_len, d_model)
        """
        batch_size, num_satellites, seq_len, d_model = satellite_features.shape

        if self.constellation_mode == 'cooperative':
            # 协同模式：使用注意力机制融合所有卫星信息
            # 重塑为 (batch_size * seq_len, num_satellites, d_model)
            sat_features_reshaped = satellite_features.permute(0, 2, 1, 3).reshape(
                batch_size * seq_len, num_satellites, d_model
            )

            # 计算平均特征作为查询
            query = sat_features_reshaped.mean(dim=1, keepdim=True)  # (batch_size * seq_len, 1, d_model)

            # 应用多头注意力
            fused_features, _ = self.constellation_fusion(
                query, sat_features_reshaped, sat_features_reshaped
            )

            # 重塑回原始形状
            constellation_features = fused_features.squeeze(1).reshape(
                batch_size, seq_len, d_model
            )

        elif self.constellation_mode == 'hybrid':
            # 混合模式：门控融合
            # 先计算注意力融合
            sat_features_reshaped = satellite_features.permute(0, 2, 1, 3).reshape(
                batch_size * seq_len, num_satellites, d_model
            )
            query = sat_features_reshaped.mean(dim=1, keepdim=True)

            attn_features, _ = self.constellation_fusion(
                query, sat_features_reshaped, sat_features_reshaped
            )
            attn_features = attn_features.squeeze(1).reshape(batch_size, seq_len, d_model)

            # 计算门控权重
            flat_features = satellite_features.permute(0, 2, 1, 3).reshape(
                batch_size, seq_len, -1
            )  # (batch_size, seq_len, num_satellites * d_model)
            gate_weights = self.gate(flat_features)  # (batch_size, seq_len, d_model)

            # 简单平均作为基础特征
            avg_features = satellite_features.mean(dim=1)  # (batch_size, seq_len, d_model)

            # 门控融合
            constellation_features = gate_weights * attn_features + (1 - gate_weights) * avg_features

        else:  # competitive mode
            # 竞争模式：简单平均，不进行信息交互
            constellation_features = satellite_features.mean(dim=1)  # (batch_size, seq_len, d_model)

        return constellation_features


class TransformerDecoderLayer(nn.Module):
    """
    Transformer解码器层，用于自回归任务选择
    """
    def __init__(self, d_model: int, n_heads: int, d_ff: int, dropout: float = 0.1):
        super(TransformerDecoderLayer, self).__init__()

        # 掩码自注意力
        self.self_attention = MultiHeadAttention(d_model, n_heads, dropout)

        # 编码器-解码器注意力
        self.cross_attention = MultiHeadAttention(d_model, n_heads, dropout)

        # 前馈网络
        self.feed_forward = FeedForward(d_model, d_ff, dropout)

        # 层归一化
        self.norm1 = nn.LayerNorm(d_model)
        self.norm2 = nn.LayerNorm(d_model)
        self.norm3 = nn.LayerNorm(d_model)

        # Dropout
        self.dropout = nn.Dropout(dropout)

    def forward(self, x: torch.Tensor, encoder_output: torch.Tensor,
                self_attn_mask: Optional[torch.Tensor] = None,
                cross_attn_mask: Optional[torch.Tensor] = None) -> torch.Tensor:
        """
        x: (batch_size, tgt_len, d_model) - 解码器输入
        encoder_output: (batch_size, src_len, d_model) - 编码器输出
        self_attn_mask: (batch_size, tgt_len, tgt_len) - 自注意力掩码
        cross_attn_mask: (batch_size, tgt_len, src_len) - 交叉注意力掩码
        """
        # 掩码自注意力 + 残差连接 + 层归一化
        self_attn_output, _ = self.self_attention(x, x, x, self_attn_mask)
        x = self.norm1(x + self.dropout(self_attn_output))

        # 编码器-解码器注意力 + 残差连接 + 层归一化
        cross_attn_output, _ = self.cross_attention(x, encoder_output, encoder_output, cross_attn_mask)
        x = self.norm2(x + self.dropout(cross_attn_output))

        # 前馈网络 + 残差连接 + 层归一化
        ff_output = self.feed_forward(x)
        x = self.norm3(x + self.dropout(ff_output))

        return x


class ConstellationTransformerDecoder(nn.Module):
    """
    星座任务规划的Transformer解码器
    用于自回归地选择任务和分配卫星
    """
    def __init__(self, d_model: int, n_heads: int, n_layers: int, d_ff: int,
                 num_satellites: int, max_seq_len: int = 1000, dropout: float = 0.1):
        super(ConstellationTransformerDecoder, self).__init__()

        self.d_model = d_model
        self.num_satellites = num_satellites
        self.max_seq_len = max_seq_len

        # 位置编码
        self.positional_encoding = PositionalEncoding(d_model, max_seq_len, dropout)

        # 解码器层
        self.decoder_layers = nn.ModuleList([
            TransformerDecoderLayer(d_model, n_heads, d_ff, dropout)
            for _ in range(n_layers)
        ])

        # 任务选择头
        self.task_selection_head = nn.Sequential(
            nn.Linear(d_model, d_model),
            nn.LayerNorm(d_model),
            nn.GELU(),
            nn.Dropout(dropout),
            nn.Linear(d_model, 1)
        )

        # 卫星选择头
        self.satellite_selection_head = nn.Sequential(
            nn.Linear(d_model * 2, d_model),  # 任务特征 + 上下文特征
            nn.LayerNorm(d_model),
            nn.GELU(),
            nn.Dropout(dropout),
            nn.Linear(d_model, num_satellites)
        )

        # 初始化参数
        self._init_parameters()

    def _init_parameters(self):
        """初始化模型参数"""
        for p in self.parameters():
            if p.dim() > 1:
                nn.init.xavier_uniform_(p)

    def forward(self, encoder_output: torch.Tensor, satellite_features: torch.Tensor,
                max_steps: int, static: torch.Tensor = None, dynamic: torch.Tensor = None,
                mask_fn=None, update_fn=None) -> Tuple[torch.Tensor, torch.Tensor, torch.Tensor]:
        """
        encoder_output: (batch_size, seq_len, d_model) - 编码器输出
        satellite_features: (batch_size, num_satellites, seq_len, d_model) - 卫星特征
        max_steps: int - 最大解码步数

        返回:
        tour_indices: (batch_size, max_steps) - 选择的任务索引
        satellite_indices: (batch_size, max_steps) - 执行任务的卫星索引
        log_probs: (batch_size, max_steps) - 对数概率
        """
        batch_size, seq_len, d_model = encoder_output.shape
        device = encoder_output.device

        # 初始化输出
        tour_indices = []
        satellite_indices = []
        log_probs = []

        # 初始化解码器输入（起始标记）
        decoder_input = torch.zeros(batch_size, 1, d_model, device=device)

        # 创建因果掩码
        def create_causal_mask(size):
            mask = torch.triu(torch.ones(size, size, device=device), diagonal=1)
            return mask == 0

        for step in range(max_steps):
            current_len = decoder_input.size(1)

            # 添加位置编码
            decoder_input_with_pos = self.positional_encoding(
                decoder_input.transpose(0, 1)
            ).transpose(0, 1)

            # 创建自注意力掩码
            self_attn_mask = create_causal_mask(current_len)
            self_attn_mask = self_attn_mask.unsqueeze(0).expand(batch_size, -1, -1)

            # 通过解码器层
            decoder_output = decoder_input_with_pos
            for decoder_layer in self.decoder_layers:
                decoder_output = decoder_layer(
                    decoder_output, encoder_output, self_attn_mask
                )

            # 获取当前步的输出
            current_output = decoder_output[:, -1, :]  # (batch_size, d_model)

            # 任务选择 - 改进版本
            task_logits = self.task_selection_head(encoder_output).squeeze(-1)  # (batch_size, seq_len)

            # 应用资源约束掩码（关键修复！）
            if mask_fn is not None and dynamic is not None:
                resource_mask, satellite_masks = mask_fn(dynamic, static=static)
                # resource_mask: (batch_size, seq_len) - 任何卫星可以执行的任务
            else:
                resource_mask = torch.ones_like(task_logits, dtype=torch.bool)
                satellite_masks = torch.ones(batch_size, seq_len, self.num_satellites, device=device)

            # 应用已选择任务的掩码
            task_mask = resource_mask.clone()
            if step > 0:
                for b in range(batch_size):
                    for prev_idx in tour_indices:
                        task_mask[b, prev_idx[b]] = False

            # 排除起始节点（索引0）
            task_mask[:, 0] = False

            # 应用掩码到logits
            task_logits[task_mask == 0] = -float('inf')

            # 添加基于收益的启发式奖励
            if step > 0:
                # 计算基于特征的奖励
                for i in range(1, encoder_output.size(1)):  # 跳过起始节点
                    if task_mask[:, i].any():
                        # 基于特征强度的奖励
                        feature_strength = encoder_output[:, i, :].norm(dim=1) * 0.1
                        task_logits[:, i] += feature_strength

            # 添加温度控制以增加探索性
            temperature = max(0.8, 2.0 - step * 0.1)  # 随步数递减的温度
            task_logits = task_logits / temperature

            # 检查是否有可选的任务（关键约束检查！）
            valid_tasks = task_mask.sum(dim=1)  # 每个样本的可选任务数
            if valid_tasks.min() == 0:
                # 如果任何样本都没有可选任务，则结束解码
                break

            # 计算任务选择概率（只对有效任务）
            task_probs = F.softmax(task_logits, dim=-1)

            # 使用掩码进行采样（正确的约束应用）
            if self.training:
                # 训练时：从有效任务中采样
                task_dist = torch.distributions.Categorical(task_probs)
                task_idx = task_dist.sample()

                # 确保选择的任务是有效的
                for b in range(batch_size):
                    while task_mask[b, task_idx[b]] == 0:
                        task_idx[b] = task_dist.sample()[b]

                task_log_prob = task_dist.log_prob(task_idx)
            else:
                # 推理时：贪婪选择最高概率的有效任务
                masked_probs = task_probs.clone()
                masked_probs[task_mask == 0] = -float('inf')
                task_idx = torch.argmax(masked_probs, dim=1)
                task_log_prob = torch.log(torch.gather(task_probs, 1, task_idx.unsqueeze(1)).squeeze(1))

            # 获取选中任务的特征
            selected_task_features = torch.gather(
                encoder_output, 1,
                task_idx.unsqueeze(1).unsqueeze(2).expand(-1, 1, d_model)
            ).squeeze(1)  # (batch_size, d_model)

            # 卫星选择 - 改进版本
            satellite_input = torch.cat([selected_task_features, current_output], dim=-1)
            satellite_logits = self.satellite_selection_head(satellite_input)  # (batch_size, num_satellites)

            # 应用卫星资源约束掩码（关键修复！）
            if mask_fn is not None and dynamic is not None:
                # 获取每颗卫星对当前任务的掩码
                for b in range(batch_size):
                    task_id = task_idx[b].item()
                    for sat_id in range(self.num_satellites):
                        if satellite_masks[b, task_id, sat_id] == 0:
                            satellite_logits[b, sat_id] = -float('inf')

            # 优化的卫星负载均衡机制
            if step > 0 and step % 5 == 0:  # 每5步才计算一次，减少计算频率
                # 使用向量化操作统计卫星任务数量
                satellite_counts = torch.zeros(batch_size, self.num_satellites, device=satellite_logits.device)

                # 向量化统计：避免嵌套循环
                sat_indices_tensor = torch.stack(satellite_indices, dim=1)  # (batch_size, step)
                for sat_id in range(self.num_satellites):
                    satellite_counts[:, sat_id] = (sat_indices_tensor == sat_id).sum(dim=1).float()

                # 简化的负载均衡惩罚
                mean_count = satellite_counts.mean(dim=1, keepdim=True)
                deviation_penalty = (satellite_counts - mean_count) * 2.0  # 减少惩罚强度
                satellite_logits = satellite_logits - deviation_penalty

            # 添加随机性以避免总是选择同一颗卫星
            temperature = 1.5  # 增加温度以增加随机性
            satellite_logits = satellite_logits / temperature

            # 检查是否有可用的卫星
            valid_satellites = torch.zeros(batch_size, device=device)
            for b in range(batch_size):
                task_id = task_idx[b].item()
                valid_satellites[b] = satellite_masks[b, task_id, :].sum()

            if valid_satellites.min() == 0:
                # 如果任何样本都没有可用卫星，则结束解码
                break

            # 计算卫星选择概率
            satellite_probs = F.softmax(satellite_logits, dim=-1)

            # 使用掩码进行卫星采样
            if self.training:
                satellite_dist = torch.distributions.Categorical(satellite_probs)
                satellite_idx = satellite_dist.sample()

                # 确保选择的卫星是有效的
                for b in range(batch_size):
                    task_id = task_idx[b].item()
                    while satellite_masks[b, task_id, satellite_idx[b]] == 0:
                        satellite_idx[b] = satellite_dist.sample()[b]

                satellite_log_prob = satellite_dist.log_prob(satellite_idx)
            else:
                # 推理时：贪婪选择
                masked_sat_probs = satellite_logits.clone()
                for b in range(batch_size):
                    task_id = task_idx[b].item()
                    for sat_id in range(self.num_satellites):
                        if satellite_masks[b, task_id, sat_id] == 0:
                            masked_sat_probs[b, sat_id] = -float('inf')

                satellite_idx = torch.argmax(masked_sat_probs, dim=1)
                satellite_log_prob = torch.log(torch.gather(satellite_probs, 1, satellite_idx.unsqueeze(1)).squeeze(1))

            # 记录结果
            tour_indices.append(task_idx)
            satellite_indices.append(satellite_idx)
            log_probs.append(task_log_prob + satellite_log_prob)

            # 更新动态状态（关键修复！）
            if update_fn is not None and dynamic is not None:
                dynamic = update_fn(static, dynamic, task_idx, satellite_idx)

            # 更新解码器输入
            next_input = selected_task_features.unsqueeze(1)  # (batch_size, 1, d_model)
            decoder_input = torch.cat([decoder_input, next_input], dim=1)

        # 转换为张量
        tour_indices = torch.stack(tour_indices, dim=1)  # (batch_size, max_steps)
        satellite_indices = torch.stack(satellite_indices, dim=1)  # (batch_size, max_steps)
        log_probs = torch.stack(log_probs, dim=1)  # (batch_size, max_steps)

        return tour_indices, satellite_indices, log_probs


class GPNTransformerConstellation(nn.Module):
    """
    基于Transformer的星座任务规划模型
    """
    def __init__(self, static_size: int, dynamic_size: int, d_model: int = 256,
                 n_heads: int = 8, n_encoder_layers: int = 6, n_decoder_layers: int = 6,
                 d_ff: int = 1024, num_satellites: int = 3,
                 constellation_mode: str = 'cooperative', dropout: float = 0.1,
                 max_seq_len: int = 1000, update_fn=None, mask_fn=None, num_nodes: int = 50,
                 use_checkpoint: bool = False):
        super(GPNTransformerConstellation, self).__init__()

        self.static_size = static_size
        self.dynamic_size = dynamic_size
        self.d_model = d_model
        self.num_satellites = num_satellites
        self.constellation_mode = constellation_mode
        self.update_fn = update_fn
        self.mask_fn = mask_fn
        self.num_nodes = num_nodes
        self.use_checkpoint = use_checkpoint

        # 输入特征大小
        input_size = static_size + dynamic_size

        # Transformer编码器
        self.encoder = ConstellationTransformerEncoder(
            input_size=input_size,
            d_model=d_model,
            n_heads=n_heads,
            n_layers=n_encoder_layers,
            d_ff=d_ff,
            num_satellites=num_satellites,
            constellation_mode=constellation_mode,
            dropout=dropout,
            max_seq_len=max_seq_len,
            use_checkpoint=use_checkpoint
        )

        # Transformer解码器
        self.decoder = ConstellationTransformerDecoder(
            d_model=d_model,
            n_heads=n_heads,
            n_layers=n_decoder_layers,
            d_ff=d_ff,
            num_satellites=num_satellites,
            max_seq_len=max_seq_len,
            dropout=dropout
        )

        # 初始化参数
        self._init_parameters()

    def _init_parameters(self):
        """初始化模型参数"""
        for p in self.parameters():
            if p.dim() > 1:
                nn.init.xavier_uniform_(p)

    def forward(self, static: torch.Tensor, dynamic: torch.Tensor) -> Tuple[torch.Tensor, torch.Tensor, torch.Tensor]:
        """
        static: (batch_size, static_size, seq_len)
        dynamic: (batch_size, dynamic_size, seq_len, num_satellites)

        返回:
        tour_indices: (batch_size, tour_len) - 选择的任务索引
        satellite_indices: (batch_size, tour_len) - 执行任务的卫星索引
        log_probs: (batch_size, tour_len) - 对数概率
        """
        batch_size, static_size, seq_len = static.shape

        # 编码阶段
        constellation_features, satellite_features = self.encoder(static, dynamic)

        # 解码阶段 - 自回归生成任务序列
        max_steps = min(seq_len - 1, self.num_nodes)  # 排除起始节点
        tour_indices, satellite_indices, log_probs = self.decoder(
            constellation_features, satellite_features, max_steps,
            static=static, dynamic=dynamic, mask_fn=self.mask_fn, update_fn=self.update_fn
        )

        # 在开头添加起始节点（索引0）
        start_indices = torch.zeros(batch_size, 1, dtype=torch.long, device=static.device)
        start_satellite_indices = torch.zeros(batch_size, 1, dtype=torch.long, device=static.device)
        start_log_probs = torch.zeros(batch_size, 1, device=static.device)

        tour_indices = torch.cat([start_indices, tour_indices], dim=1)
        satellite_indices = torch.cat([start_satellite_indices, satellite_indices], dim=1)
        log_probs = torch.cat([start_log_probs, log_probs], dim=1)

        return tour_indices, satellite_indices, log_probs


class TransformerStateCritic(nn.Module):
    """
    基于Transformer的状态评论家网络
    """
    def __init__(self, static_size: int, dynamic_size: int, d_model: int = 256,
                 n_heads: int = 8, n_layers: int = 4, d_ff: int = 1024,
                 num_satellites: int = 3, constellation_mode: str = 'cooperative',
                 dropout: float = 0.1, max_seq_len: int = 1000):
        super(TransformerStateCritic, self).__init__()

        self.num_satellites = num_satellites
        self.constellation_mode = constellation_mode

        # 使用与策略网络相同的编码器
        input_size = static_size + dynamic_size
        self.encoder = ConstellationTransformerEncoder(
            input_size=input_size,
            d_model=d_model,
            n_heads=n_heads,
            n_layers=n_layers,
            d_ff=d_ff,
            num_satellites=num_satellites,
            constellation_mode=constellation_mode,
            dropout=dropout,
            max_seq_len=max_seq_len
        )

        # 价值估计头
        self.value_head = nn.Sequential(
            nn.Linear(d_model, d_model // 2),
            nn.LayerNorm(d_model // 2),
            nn.GELU(),
            nn.Dropout(dropout),
            nn.Linear(d_model // 2, d_model // 4),
            nn.LayerNorm(d_model // 4),
            nn.GELU(),
            nn.Dropout(dropout),
            nn.Linear(d_model // 4, 1)
        )

        # 初始化参数
        self._init_parameters()

    def _init_parameters(self):
        """初始化模型参数"""
        for p in self.parameters():
            if p.dim() > 1:
                nn.init.xavier_uniform_(p)

    def forward(self, static: torch.Tensor, dynamic: torch.Tensor) -> torch.Tensor:
        """
        static: (batch_size, static_size, seq_len)
        dynamic: (batch_size, dynamic_size, seq_len, num_satellites)

        返回:
        value: (batch_size,) - 状态价值估计
        """
        # 获取星座级别的特征
        constellation_features, _ = self.encoder(static, dynamic)

        # 全局池化：对序列维度进行平均池化
        global_features = constellation_features.mean(dim=1)  # (batch_size, d_model)

        # 价值估计
        value = self.value_head(global_features).squeeze(-1)  # (batch_size,)

        return value
